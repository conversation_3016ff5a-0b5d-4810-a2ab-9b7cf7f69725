const { BinaryRepo } = require('./binary-repo');

const MemQueryParams = {

    /** 多个目标账号（格式： 101;102;103） */
    account_ids: null, 
    /** 交易类型 */
    trade_type: null,
    /** 交易员ID */
    trade_user_id: null,

    /** 更新时间（增量查询条件） */
    update_time: null,

    /** 分页大小（首屏数据查询条件） */
    pageSize: null,
    /** 页码（首屏数据查询条件） */
    pageNo: null,
};

class OrderRepository extends BinaryRepo {

    add(data) {

        return new Promise((resolve, reject) => {

            this.http('/order/add', { method: 'post', params: { account_id: data[0].accountId }, data }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    update(data) {

        return new Promise((resolve, reject) => {

            this.http('/order/update', { method: 'post', params: { account_id: data[0].accountId }, data }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    delete(data) {

        return new Promise((resolve, reject) => {

            this.http('/order/delete', { method: 'delete', params: { account_id: data[0].accountId }, data }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    /**
     * 请求母单下挂子单
     * @param {Number|String} parentOrderId
     */
    requestBelongOrders(parentOrderId) {

        return new Promise((resolve, reject) => {

            this.http('/order/page', { method: 'post', data: { parentOrderId, pageSize: 999999, pageNo: 1 } }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    /**
     * 请求，今日订单 > 首页数据
     * @param {Number|String} identityId 
     * @param {Number} pageSize 
     */
    requestFirstPage(identityId, pageSize) {

        return new Promise((resolve, reject) => {

            this.http('/order/page', { method: 'post', data: { identityId, pageSize, pageNo: 1 } }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    /**
     * 请求首屏，委托数据
     * @returns {{
        * errorCode,
        * errorMsg,
        * data: {
            * pageNo,
            * pageSize,
            * totalPages,
            * totalSize,
            * list: [],
            * sort: [{ property, direction }]
        * },
     * }}
     */
    quickMemQuery(params = MemQueryParams) {

        return new Promise((resolve, reject) => {

            this.http('../v4/mem/order/page', { method: 'get', params, headers: this.headers, responseType: this.responseType }).then(
                (resp) => { resolve(this.translate(resp)); },
                (error) => { reject(error); },
            );
		});
    }

    /**
     * 请求全量|增量，委托数据
     * @returns {{
        * errorCode,
        * errorMsg,
        * data: Array<Array>,
     * }}
     */
    batchMemQuery(params = MemQueryParams) {

        return new Promise((resolve, reject) => {

            this.http('../v4/mem/order', { method: 'get', params, headers: this.headers, responseType: this.responseType }).then(
                (resp) => { resolve(this.translate(resp)); },
                (error) => { reject(error); },
            );
		});
    }

    /**
     * 查询【前序总量】与【涨停封单量】
     */
    queryFrontOrder() {

        return new Promise((resolve, reject) => {

            this.http('/order/volume').then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
		});
    }

    /**
     * 查询【前序总量】与【涨停封单量】（拆分后的独立行情）
     * @param {Array<{ strategyId, instrument, orderNo }>} conditions
     */
    queryFrontOrderSummary(conditions) {

        return new Promise((resolve, reject) => {

            this.http('/order/volume/summary', { method: 'post', data: conditions }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
		});
    }
}

module.exports = { repoOrder: new OrderRepository() };
