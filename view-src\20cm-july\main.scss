.popout-trading-window {

	.trade-view-block {
		height: 100%;
		padding-bottom: 36px;
		box-sizing: border-box;
	}
}

.view-main-inter {

	height: 100%;
	display: flex;
	flex-direction: column;
	box-sizing: border-box;

	&.full-occupied {
		
		padding-bottom: 0;
		.part-bottom {
			display: none;
		}
	}

	.view-toolbar {

		position: relative;
		height: 28px;
		// margin-top: -28px;
		line-height: 28px;
		overflow: hidden;
		background-color: #253650;

		.el-input,
		.el-select,
		.el-autocomplete,
		.el-input__inner {
			height: 24px;
		}

		.el-input__icon {
			line-height: 24px;
		}

		.fixed-right-icon {
			position: absolute;
			right: 0;
			top: 0;
			z-index: 1;
		}
	}

	.part-top {

		flex-grow: 1;
		flex-shrink: 1;
		height: 99px;
		box-sizing: border-box;
	}
	
	.part-bottom {
		
		flex-grow: 0;
		flex-shrink: 0;
		height: 290px;
		display: flex;
		justify-content: flex-start;
		box-sizing: border-box;
	}

	.view-level2 {

		height: 100%;
		width: 200px;
		flex-grow: 0;
		flex-shrink: 0;
		box-sizing: border-box;

		.level-item {

			height: 23px;
			line-height: 23px;
			padding-left: 10px;
			padding-right: 10px;

			&:hover {
				background-color: lightsalmon;
			}

			> * {

				display: block;
				float: left;
			}
		}

		.level-item-0 {
			margin-top: 10px;
		}

		.level-item-4 {
			margin-bottom: 5px;
		}

		.level-item-5 {

			padding-top: 5px;
			border-top: 1px solid #000;
		}

		.level-name {
			width: 20%;
		}

		.level-price {
			
			width: 40%;
			text-align: right;
		}

		.level-hands {

			width: 40%;
			text-align: right;
		}
	}

	.view-trading {

		flex-grow: 0;
		flex-shrink: 0;
		height: 100%;
		width: 300px;
		box-sizing: border-box;
		border-left: 4px solid #0C1016;
	}

	.trading-panel {

		padding: 0 10px 10px 10px;

		.el-radio-group {
			width: 100%;
		}

		.el-radio-button {
			width: 50%;
		}

		.el-radio-button__inner {
			width: 100%;
		}

		.input-row {

			box-sizing: border-box;
			margin-top: 10px;
			height: 24px;
			padding-left: 40px;
		}

		.input-text {

			display: block;
			float: left;
			margin-left: -40px;
			line-height: 28px;
		}

		.input-ctr {

			box-sizing: border-box;
			width: 100%;

      .fix {
        margin-left: 10px;
        width: 88%;
      }

			&.with-unit {

				padding-right: 25px;

				.ctr-unit {

					display: inline-block;
					margin-right: -25px;
					width: 25px;
					text-align: center;
				}
			}
		}

		.link-btn {

			cursor: default;
			&:hover {
				text-decoration: underline;
			}
		}

		.ratios .link-btn {

			display: block;
			float: left;
			width: 16%;
			font-size: 14px;
		}

		.custom-ratio {

			display: inline-block;
			position: relative;
			top: -6px;

			.el-input-number {
				
				position: relative;
				top: 0;
				margin-left: 4px;
				width: 60px;
			}

			.el-input__inner {

				padding-left: 5px;
    			padding-right: 5px;
				border-radius: 4px;
			}
		}
	}

	.by-volume-flag,
	.by-amount-flag {
		
		background-color: #245BB0;
		border-radius: 4px;
		width: 34px;
		text-align: center;
	}

	.by-amount-flag {
		color: red;
	}

	.concentrated-input {

		.el-input__inner {
			letter-spacing: 1px;
			font-size: 16px;
		}
	}

	.cancel-option-ctr {

		margin: 0 10px;
		width: 120px;

		.el-input__inner {
			height: 24px;
		}
	}

	.view-sell-task-ext {

		min-width: 100px;
		width: 100px;
		flex-grow: 1;
		flex-shrink: 0;
		padding-left: 2px;
		overflow-y: hidden;
		overflow-x: auto;
		box-sizing: border-box;

		.view-sell-task {

			padding-top: 28px;
			box-sizing: border-box;

			.title {

				height: 28px;
				margin-top: -28px;
				line-height: 28px;
				background-color: #253650;
			}
		}
	}

	.tabbed-radios-external {

		.el-radio-group {
			margin-bottom: -1px;
		}

		.el-radio-button__inner {

			border-top-left-radius: 6px;
			border-top-right-radius: 6px;
			border: none;
			background-color: transparent;
		}

		.el-radio-button__orig-radio:checked+.el-radio-button__inner {
			background-color: #1A212B;
		}

		.el-radio-button--mini .el-radio-button__inner {
			padding: 5px 15px;
		}
	}
}