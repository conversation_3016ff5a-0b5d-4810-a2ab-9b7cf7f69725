<div class="trade-view-block">

	<div class="view-toolbar s-pdl-10 s-pdr-10">
		<template>
			<span class="title">{{ text }}</span>
			<span class="s-pdl-10 s-fs-16">
				<template v-if="states.instrument">({{ states.instrumentName }}/{{ shortizeCode(states.instrument) }})</template>
				<template v-else>N/A</template>
			</span>
			<span class="s-pdl-20 s-fs-16">买一量: 
				<span class="s-color-red">{{ typeof states.buy1 == 'number' ? thousandsInt(states.buy1) + '手' : 'N/A' }}</span>
			</span>
			<span class="s-pdl-20 s-fs-16">前序总量: 
				<span class="s-color-red">{{ typeof states.ahead == 'number' ? thousandsInt(states.ahead) + '手' : 'N/A' }}</span>
			</span>
      		<span class="s-pdl-20 s-fs-16">前序总额: 
				<span class="s-color-red">{{ typeof states.aheadAmount == 'number' ? thousandsInt(states.aheadAmount) + '万元' : 'N/A' }}</span>
			</span>
			<span class="s-pdl-20 s-fs-16">封单笔数: 
				<span class="s-color-red">{{ typeof states.total == 'number' ? thousandsInt(states.total) : 'N/A' }}</span>
			</span>
			<span v-if="isFrontCancelOn" class="s-pdl-20 s-fs-16">前序撤单: 
				<span class="s-color-red">{{ typeof states.front_cancel == 'number' ? thousandsInt(states.front_cancel) + '手' : 'N/A' }}</span>
			</span>
		</template>
	</div>

	<div class="s-full-height">

		<div v-if="remotes.length == 0" style="display: table; height: 100%; width: 100%;">
			<div style="display: table-cell; text-align: center; vertical-align: middle;">无队列数据...</div>
		</div>
		
		<ul v-else class="infinite-list" v-infinite-scroll="() => { loadMore(); }">
			<li
				v-for="hands in remotes"
				:class="hasMatched(hands) == true ? 'highlighted' : ''"
				class="infinite-list-item remote themed-right-border themed-bottom-border s-ellipsis">{{ hands }}</li>
		</ul>

	</div>

</div>
