
const fs = require('fs');
const { helper } = require('./helper');
const { LocalSetting } = require('../config/system-setting.local');
const { systemEnum } = require('../config/system-enum');
const { isRenderProcess } = require('../config/environment');

const CompletedOrderStatusCodeMap = {
    [systemEnum.orderStatus.partialCanceled.code]: true, 
    [systemEnum.orderStatus.traded.code]: true, 
    [systemEnum.orderStatus.invalid.code]: true, 
    [systemEnum.orderStatus.canceled.code]: true 
};

const CachedData = {

    stocks: null,
    stocksHash: null,
    futures: null,
    futuresHash: null,
    options: null,
    optionsHash: null,
};

if (isRenderProcess) {
    try2ResetAndReUpdate();
}

/**
 * 通过重置缓存的股票合约，强制从文件重新读取
 */
function try2ResetAndReUpdate() {
    
    const remote = require('@electron/remote');
    const required = remote.app.contextData['stock-reupdate-required'];
    if (required != 1) {
        return;
    }

    let now = new Date();
    let now_ts = now.getTime();
    let today_0901_ts = new Date(now.format('yyyy-MM-dd 09:01:00')).getTime();

    if (now_ts >= today_0901_ts) {
        return;
    }

    let delay = Math.max(60 * 1000, today_0901_ts - now_ts);
    setTimeout(() => { CachedData.stocks = null; }, delay);
}


function ReadRecords(filePath) {

    if (!fs.existsSync(filePath)) {
        return [];
    }

    try {
        let content = fs.readFileSync(filePath, { encoding: 'utf8' });
        let list = JSON.parse(content);
        return list;
    }
    catch(ex) {
        console.error(ex);
        return [];
    }
}

const OneInstrument = {

    assetType: 0, 
    creditBuy: 0,
    exchangeId: '', 
    instrument: '', 
    instrumentName: '', 
    isCollateral: 0, 
    lastPrice: 0, 
    lowerLimitPrice: 0, 
    optionType: 0, 
    preClosePrice: 0, 
    /** 价格步长（如：0.01） */
    priceTick: 0, 
    /** 行权价格（期权only） */
    strikePrice: 0, 
    upperLimitPrice: 0, 
    /** 合约乘数 */
    volumeMultiple: 0, 
    py: '',
    /** 价格保留小数位数精度（如：2） */
    pricePrecision: 0,


    /** 期货、期权，合约过期日期 */
    expireDate: undefined,
    /** 期权针对目标合约代码 */
    underlyingInstrument: undefined,
    /** 合约搜索，相似度信息 */
    similarity: undefined,
    /** 合约搜索，匹配位置信息 */
    similarity2: undefined,
};

const BizHelper = {

    /**
     * 股票合约全集
     * @returns {Array<OneInstrument>}
     */
    get stocks() {
		return CachedData.stocks || (CachedData.stocks = ReadRecords(LocalSetting.getMarketStockPath()));
    },
    
    /**
     * 股票合约全集map
     */
    get stocksHash() {
        return CachedData.stocksHash || (CachedData.stocksHash = helper.array2Dict(this.stocks, ins => `${ins.exchangeId}.${ins.instrument}`));
    },

	/**
     * 期货合约全集
     * @returns {Array<OneInstrument>}
     */
    get futures() {
        return CachedData.futures || (CachedData.futures = ReadRecords(LocalSetting.getMarketFuturePath()));
    },

    /**
     * 期货合约全集map
     */
    get futuresHash() {
        return CachedData.futuresHash || (CachedData.futuresHash = helper.array2Dict(this.futures, ins => `${ins.exchangeId}.${ins.instrument}`));
    },

    /**
     * 期权合约全集
     * @returns {Array<OneInstrument>}
     */
    get options() {
        return CachedData.options || (CachedData.options = ReadRecords(LocalSetting.getMarketOptionPath()));
    },

    /**
     * 期权合约全集map
     */
    get optionsHash() {
        return CachedData.optionsHash || (CachedData.optionsHash = helper.array2Dict(this.options, ins => `${ins.exchangeId}.${ins.instrument}`));
    },

    _pickFromHash(instrument) {
        return this.stocksHash[instrument] || this.futuresHash[instrument] || this.optionsHash[instrument];
    },

    /**
     * 获取一个合约信息
     * @param {String} instrument 
     * @returns {OneInstrument}
     */
    pick(instrument) {
        
        let stockInfo = this._pickFromHash(instrument);
        if (!stockInfo) {
            return { instrument, instrumentName: instrument };
        }

        let struc = {};
        Object.assign(struc, stockInfo);
        struc.instrument = instrument;
        return struc;
    },

    /**
     * 获取具体合约，对应的最小价格变动，如：股票0.01，某期货合约0.5
     * @param {String} instrument 合约代码
     */
    getPriceTick(instrument) {

        let matched = this._pickFromHash(instrument);
        return matched && typeof matched.priceTick == 'number' ? matched.priceTick : 0.01;
    },

    /**
     * 获取具体合约，对应的标准价格精度（价格小数点后，小数位数），如：股票2，某期货合约0，某期权1
     * @param {String} instrument 合约代码
     */
    getPricePrecision(instrument) {

        let matched = this._pickFromHash(instrument);
        return matched && typeof matched.pricePrecision == 'number' ? matched.pricePrecision : 2;
    },

    /**
     * 获取具体合约，对应的数量乘数，如：股票1，某期货10
     * @param {String} instrument 合约代码
     */
    getVolumeMultiple(instrument) {

        let matched = this._pickFromHash(instrument);
        if (matched === undefined) {
            return 1;
        }
        else {
            return typeof matched.volumeMultiple == 'number' ? matched.volumeMultiple : 1;
        }
    },

    /**
     * 将一个订单数据标准化为，应用程序期望的格式
     * @param {Object} order 订单数据
     * @param {Boolean} options { standarize_precision: 是否根据该合约的品种，保持相应的价格小数精度 }
     */
    reshapeOrder(order, options = { standarize_precision: false }) {

        /**
         * todo: refactor this method
         */

        if(!order) {
            return order;
        }

        order.tradedAmount = typeof order.tradedPrice == 'number' ? order.tradedVolume * order.tradedPrice : 0;
        order.floatProfit = 0;
        order.isCompleted = CompletedOrderStatusCodeMap[order.orderStatus] === true;
        helper.extendMembers(order, ['createTime', 'orderTime', 'tradeTime', 'exchangeOrderId', 'strategyName', 'remark'], '');

        if(options.standarize_precision === true) {

            let precision = this.getPricePrecision(order.instrument);
            typeof order.orderPrice == 'number' ? order.orderPrice = +order.orderPrice.toFixed(precision) : null;
            typeof order.tradedPrice == 'number' ? order.tradedPrice = +order.tradedPrice.toFixed(precision) : null;
        }

        return order;
    },

    /**
     * 将一个持仓数据标准化为，应用程序期望的格式
     * @param {Object} position  持仓数据
     * @param {Object} options { calculateClosableVolume：是否计算closable volume / considerPercision：是否需要需要对价格信息做精度处理 }
     */
    reshapePosition(position, options = { calculateClosableVolume: false, considerPercision: false }) {

        /**
         * todo: refactor this method
         */

        position.lastPrice = null;
        position.floatProfit = null;
        position.profit = null;
        position.totalPosition = (position.yesterdayPosition || 0) + (position.todayPosition || 0);

        if(options.calculateClosableVolume === true) {
            
            var is_future_account = position.assetType === systemEnum.assetsTypes.future.code;
            position.closableVolume = (position.yesterdayPosition || 0)
                                    - (position.frozenVolume || 0)
                                    - (position.frozenTodayVolume || 0)
                                    + (is_future_account ? position.todayPosition || 0 : 0);
        }

        if(options.considerPercision === true && typeof position.avgPrice == 'number') {
            
            let precision = this.getPricePrecision(position.instrument);
            position.avgPrice = +position.avgPrice.toFixed(precision);
        }
        
        return position;
    },

    /**
     * 将一个成交数据标准化为，应用程序期望的格式
     * @param {Object} position  持仓数据
     * @param {Object} options { considerPercision：是否需要需要对价格信息做精度处理 }
     */
    reshapeExchange(exchange, options = { considerPercision: false }) {

        /**
         * todo: refactor this method
         */

        if(options.considerPercision === true && typeof exchange.tradedPrice == 'number') {
            
            let precision = this.getPricePrecision(exchange.instrument);
            exchange.tradedPrice = +exchange.tradedPrice.toFixed(precision);
        }
        return exchange;
    },

    /**
     * 根据资产类型和关键字，过滤出满足条件的合约列表
     * @param {*} asset_type 资产类型 （按股票搜索的结果里，包含：股票、基金、债券、回购）
     * @param {String} keywords 关键字
     * @param {Boolean} workWithExchangeId 是否叠加交易所前缀
     * @returns {Array<OneInstrument>}
     */
    filterInstruments(asset_type, keywords, workWithExchangeId = false) {

        if (typeof keywords != 'string' || keywords.trim().length == 0) {
            return [];
        }

        let instruments = asset_type == systemEnum.assetsType.stock.code ? this.stocks :
                          asset_type == systemEnum.assetsType.future.code ? this.futures : this.options;
                          
        let trimed_kw = keywords.trim();
        let upper_kw = trimed_kw.toUpperCase();
        let len_of_kw = trimed_kw.length;
        let max_allowed = 30;
        let matches = [];

        for (let theIns of instruments) {

            if (theIns.assetType != asset_type) {
                continue;
            }

            let { instrument, instrumentName, exchangeId, py } = theIns;
            let formedInstrument = workWithExchangeId === true ? (exchangeId + '.' + instrument) : instrument;
            let idx_ins = Math.max(-1, formedInstrument.indexOf(trimed_kw), formedInstrument.indexOf(upper_kw));
            let idx_ins_name = Math.max(-1, instrumentName.indexOf(trimed_kw));
            let idx_py = Math.max(-1, (py || '').indexOf(upper_kw));

            if (idx_ins == -1 && idx_ins_name == -1 && idx_py == -1) {
                continue;
            }

            let weight_ins = idx_ins >= 0 ? len_of_kw / formedInstrument.length : 0;
            let weight_ins_2 = idx_ins >= 0 ? idx_ins : 0;
            let weight_ins_name = idx_ins_name >= 0 ? len_of_kw / instrumentName.length : 0;
            let weight_ins_name_2 = idx_ins_name >= 0 ? idx_ins_name : 0;
            let weight_py = idx_py >= 0 ? len_of_kw / py.length : 0;
            let weight_py_2 = idx_py >= 0 ? idx_py : 0;
            let similarity = parseInt(99999999 * Math.max(weight_ins, weight_ins_name, weight_py));
            let similarity_2 = Math.min(weight_ins_2, weight_ins_name_2, weight_py_2);
            let struc = Object.assign({}, theIns);

            struc.similarity = similarity;
            struc.similarity2 = similarity_2;
            struc.instrument = exchangeId + '.' + instrument;
            matches.push(struc);
        }
        
        matches.orderByDesc(x => x.similarity);
        matches.sort((a, b) => {
            
            let ak1 = fill0(a.similarity, 8);
            let ak2 = fill0(a.similarity2, 3);
            let bk1 = fill0(b.similarity, 8);
            let bk2 = fill0(b.similarity2, 3);
            return helper.compare(ak1 + ak2, bk1 + bk2);
        });

        return matches.slice(0, max_allowed);
    },
};

/**
 * @param {number} value 
 * @param {number} max 
 */
function fill0(value, max) {

    value = value.toString();
    while (value.length < max) {
        value = '0' + value;
    }

    return value;
}

module.exports = { BizHelper, OneInstrument };
