const { BaseView } = require('./base-view');
const { Splitter } = require('../../component/splitter');
const { SmartTable } = require('../../libs/table/smart-table');
const { Cm20FunctionCodes } = require('../../config/20cm');
const { BuyTask, ZtStandardStrategy, FromTable, RowBehaviors, ZtSetting } = require('./objects');
const { BoughtOrderFrontOrder } = require('../../model/front-order');
const { repoOrder } = require('../../repository/order');
const { BizHelper } = require('../../libs/helper-biz');
const MouseTrap = require('mousetrap');

module.exports = class OrderedTaskView extends BaseView {

    constructor() {
        
        super('@20cm-july/task-ordered', false, '已买监控');
        this.ztsetting = ZtSetting.makeDefault();
    }

    /**
     * @returns {BuyTask}
     */
    typeds(data) {
        return data;
    }
    
    /**
     * @param {BuyTask} record 
     */
    identify(record) {
        return record.id;
    }

    createTable() {

        const $table = this.$container.querySelector('table');
        const ref = new SmartTable($table, this.identify, this, {

            tableName: 'smt-20cm-task-ordered',
            displayName: this.title,
            defaultSorting: { prop: 'createTime', direction: 'desc' },
            rowSelected: this.handleRowSelect.bind(this),
            rowDbClicked: this.handleRowDbClick.bind(this),
            allRowsChecked: this.handleAllCheckChange.bind(this),
        });

        ref.setPageSize(99999);
        this.tableObj = ref;
    }

    createTable2() {

        var $table = this.$container.querySelector('.table-task-2');
        const table = new SmartTable($table, this.identify, this, {

            tableName: 'smt-20cm-task-ordered-2',
            displayName: this.title,
            defaultSorting: { prop: 'createTime', direction: 'desc' },
            rowSelected: this.handleRowSelect.bind(this),
            rowDbClicked: this.handleRowDbClick.bind(this),
        });

        table.setPageSize(99999);
        this.tableObj2 = table;
    }

    /**
     * @param {BuyTask} task 
     */
    handleRowSelect(task) {

        if (task.flagAsDiscarded === true) {
            return;
        }

        this.log(`user selected an ordered task: ${JSON.stringify(task)}`);
        const from = this.tableObj.hasRow(this.identify(task)) ? FromTable.ordered : FromTable.ordered2;
        if (from === FromTable.ordered) {
            this.tableObj2.unselectRow();
        }
        else {
            this.tableObj.unselectRow();
        }
        this.tolerate(from, RowBehaviors.select, task);
    }

    /**
     * @param {BuyTask} task 
     */
    handleRowDbClick(task) {

        if (!this.ztsetting.doubleClick2Cancel) {
            return;
        }

        if (task.flagAsDiscarded === true) {
            return;
        }

        this.log(`user double clicked an ordered task: ${JSON.stringify(task)}`);
        this.cancelTask(task);
    }

    /**
     * @param {boolean} is_all_checked 是否全选
     */
    handleAllCheckChange(is_all_checked) {

        /**
         * 上方的表格，全勾选、全不勾选事件，同步下方表格勾选状态
         */

        if (is_all_checked) {
            this.tableObj2.checkAll();
        }
        else {
            this.tableObj2.uncheckAll();
        }
    }

    createSplitter() {

        let $splitter = this.$container.querySelector('.splitter-line');

        /**
         * 未下单的两个任务列表
         */
        this.splitter = new Splitter('20cm-product-task', $splitter, this.handleSpliting.bind(this), {

            previousMinHeight: 80,
            nextMinHeight: 80,
        });

        setTimeout(() => { this.splitter.recover(); }, 1000);
        this.thisWindow.on('resize', () => { this.splitter.sync(); });
    }

    /**
     * @param {Number} previous_height
     * @param {Number} next_height
     */
    handleSpliting(previous_height, next_height) {
        //
    }

    tolerate(from, behavior, task) {
        this.trigger('tolerate', from, behavior, task);
    }

    sortableRows() {

        var ref = $(this.tableObj.$bodyTable.querySelector('tbody'));
        var ref2 = $(this.tableObj2.$bodyTable.querySelector('tbody'));
        ref.sortable();
        ref2.sortable();
    }

    refresh() {

        this.interaction.showSuccess('刷新请求已发出');
        this.trigger('refresh-task');
    }

    download() {
        this.tableObj.exportAllRecords();
    }

    highlightCol() {
        return 's-color-red';
    }

    /**
     * @param {BuyTask} task 
     */
    moveDown(task) {
        
        this.log(`to move an ordered task down: ${JSON.stringify(task)}`);
        this.tableObj.deleteRow(this.identify(task));
        this.tableObj2.putRow(task);
    }

    /**
     * @param {BuyTask} task 
     */
    moveUp(task) {
        
        this.log(`to move an ordered task up: ${JSON.stringify(task)}`);
        this.tableObj2.deleteRow(this.identify(task));
        this.tableObj.putRow(task);
    }

    /**
     * @param {BuyTask} task 
     */
    cancelTask(task) {
        
        this.log(`to cancel an ordered task: ${JSON.stringify(task)}`);
        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.cancel, { id: task.id });
        this.interaction.showSuccess('撤单请求已提交');
    }

    /**
     * @param {BuyTask} task 
     */
    removeTask(task) {
        
        this.log(`to remove a discarded task: ${JSON.stringify(task)}`);
        const row_key = this.identify(task);

        if (this.tableObj.hasRow(row_key)) {
            this.tableObj.deleteRow(row_key);
        }
        else if (this.tableObj2.hasRow(row_key)) {
            this.tableObj2.deleteRow(row_key);
        }
    }

    /**
     * @param {BuyTask} task 
     */
    outcancel(task) {

        if (!this.tableObj.hasRow(this.identify(task))) {
            return this.interaction.showError(`未匹配到可撤监控：${task.instrumentName}，请尝试从列表操作！`);
        }

        this.cancelTask(task);
    }

    /**
     * @param {BuyTask} task 
     * @param {string} instrument
     */
    formatCodeCol(task, instrument) {
        return task.flagAsDiscarded === true ? '--' : this.shortizeCode(instrument);
    }

    /**
     * @param {BuyTask} task 
     * @param {number} timestamp
     */
    formatTimeCol(task, timestamp) {
        return task.flagAsDiscarded === true ? '--' : new Date(timestamp).format('hh:mm:ss');
    }

    /**
     * @param {BuyTask} task 
     */
    formatCellValue(task, value, name) {
        return task.flagAsDiscarded === true || this.helper.isNone(value) ? '--' : value;
    }

    /**
     * @param {BuyTask} task 
     * @param {number} value 
     */
    format2Int(task, value, name) {

        if (task.flagAsDiscarded === true || this.helper.isNone(value) || typeof value != 'number') {
            return '--';
        }
        else {
            return value.thousands();
        }
    }

    /**
     * @param {BuyTask} task 
     * @param {number} value 
     */
    format2Float(task, value, name) {

        if (task.flagAsDiscarded === true || this.helper.isNone(value) || typeof value != 'number') {
            return '--';
        }
        else if (value >= 10000) {
            return (value / 10000).thousandsDecimal() + '万';
        }
        else {
            return value.thousandsDecimal();
        }
    }

    /**
     * @param {BuyTask} task 
     * @param {number} value 
     */
    format2SimpleAmount(task, value, name) {

        console.log({ task, value, name });
        if (task.flagAsDiscarded === true || this.helper.isNone(value) || typeof value != 'number') {
            return '--';
        }
        
        return this.helper.simplifyAmount(value);
    }

    /**
     * @param {BuyTask} task 
     */
    formatMove1(task) {
        return task.flagAsDiscarded === true ? '--' : '<button event.onclick="moveDown">放下面</a>';
    }

    /**
     * @param {BuyTask} task 
     */
    formatMove2(task) {
        return task.flagAsDiscarded === true ? '--' : '<button event.onclick="moveUp">放上面</a>';
    }

    /**
     * @param {BuyTask} task 
     */
    formatOper(task) {
        return task.flagAsDiscarded === true ? '<button class="danger" event.onclick="removeTask">删除</a>' : '<button class="danger" event.onclick="cancelTask">撤单</a>';
    }
    
    /**
     * @param {Array<BuyTask>} tasks
     */
    push(tasks) {

        tasks.forEach(item => {

            const row_key = this.identify(item);
            const should_keep = (this.isTaskOrdered(item.status) || this.isSupplemented(item.status)) && !item.hasCanceled;
            const t1 = this.tableObj;
            const t2 = this.tableObj2;
            const in_t1 = t1.hasRow(row_key);
            const in_t2 = t2.hasRow(row_key);
            const is_new = !(in_t1 || in_t2);

            if (should_keep) {

                if (is_new) {

                    // 新增，默认插入到上面表格
                    t1.insertRow(item);
                    this.ring4TaskBeenMonitored(item);
                }
                else {

                    const matched = this.typeds(in_t1 ? t1.getRowData(row_key) : in_t2 ? t2.getRowData(row_key) : null);
                    const is_discarded_before = matched && matched.flagAsDiscarded === true;

                    if (in_t1) {
                        t1.updateRow(item);
                    }
                    else if (in_t2) {
                        t2.updateRow(item);
                    }

                    if (is_discarded_before) {
                        this.ring4TaskBeenMonitored(item);
                    }
                }
            }
            else {

                const tobj = in_t1 ? t1 : in_t2 ? t2 : null;

                if (tobj) {

                    item.flagAsDiscarded = true;
                    tobj.updateRow(item);
                }

                if (item.hasCanceled) {
                    this.ring4Canceled(item);
                }
            }
        });

        if (this.hasInitializedFrontOrder == undefined) {

            this.hasInitializedFrontOrder = true;
            this.updateFrontOrder();
        }
    }

    /**
     * @param {BuyTask} task 来源监控任务
     * @param {ZtStandardStrategy} strategy 
     */
    outupdate(task, strategy) {

        let row_key = this.identify(task);
        let expected = this.typeds(this.tableObj.getRowData(row_key));
        if (!expected) {
            return;
        }

        this.tableObj.updateRow({

            id: expected.id,
            localId: expected.localId,
            strategyName: strategy.name,
            strategy: strategy,
        });

        this.log(`update an ordered task strategy from outside: task id/${task.id}, strategy/${JSON.stringify(strategy)}`);
        this.toSubmit(expected, Cm20FunctionCodes.request.modify, true);
    }

    createToolbarApp() {
        
        const vappIns = new Vue({

            el: this.$container.querySelector('.view-toolbar'),
            data: {
                //
            },
            methods: this.helper.fakeVueInsMethod(this, [
                this.cancelChecks,
            ]),
        });
    }

    cancelChecks() {

        var list1 = this.tableObj.extractCheckedRecords().map(x => this.typeds(x));
        var list2 = this.tableObj2.extractCheckedRecords().map(x => this.typeds(x));
        var checkes = [...list1, ...list2];
        var undiscardeds = checkes.filter(x => x.flagAsDiscarded !== true);

        if (checkes.length == 0) {
            return this.interaction.showError('未有勾选的监控');
        }
        else if (undiscardeds.length == 0) {
            return this.interaction.showError('勾选的监控已作废');
        }

        this.log(`to cancel checked tasks: ${JSON.stringify(undiscardeds.map(x => ({ instrument: x.instrument, id: x.id })))}`);
        undiscardeds.forEach(x => {
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.cancel, { id: x.id });
        });
        this.interaction.showSuccess('撤单请求已提交，数量 = ' + undiscardeds.length);
    }

    /**
     * @param {ZtSetting} setting 
     */
    setAsSetting(setting) {
        Object.assign(this.ztsetting, this.helper.deepClone(setting));
        const ref = MouseTrap;
        if (this.cachedCancelStroke) {
          ref.unbind(this.cachedCancelStroke, 'keyup');
        }
        if (this.ztsetting.cancelStroke) {
          this.cachedCancelStroke = this.ztsetting.cancelStroke.toLowerCase();
          ref.bind(this.cachedCancelStroke, this.shortcutCancel.bind(this), 'keyup');
        }
    }

    shortcutCancel() {
      var selected = this.tableObj.selectedRow;
      if (selected) {
        this.cancelTask(selected);
      }
      var selected2 = this.tableObj2.selectedRow;
      if (selected2) {
        this.cancelTask(selected2);
      }
    }


    /**
     * @returns {Array<BuyTask>}
     */
    get allTasks() {
        return this.tableObj.extractAllRecords();
    }

    async updateFrontOrder() {

        if (this.isProcessing) {
            return;
        }

        const tables = [this.tableObj, this.tableObj2].filter(x => x.rowCount > 0);
        if (tables.length == 0) {
            return;
        }

        try {

            let qconditions = this.allTasks.map(item => {
                let { id, instrument, orderNo } = item;
                return { strategyId: id, instrument, orderNo };
            });

            this.isProcessing = true;
            let resp = await repoOrder.queryFrontOrderSummary(qconditions);
            let { errorCode, errorMsg, data } = resp;

            if (errorCode == 0 && this.helper.isJson(data)) {

                let fronts = BoughtOrderFrontOrder.convert(data);
                fronts.forEach(item => {
                    
                    tables.forEach(tobj => {

                        /**
                         * 对匹配的数据行进行更新
                         */
                        if (tobj.hasRow(item.orderId)) {

                            let row = tobj.getRowData(item.orderId);
                            tobj.updateRow({

                                id: item.orderId,
                                frontOrder: item.frontOrder / 100,
                                frontOrderAmount: (item.frontOrder * BizHelper.pick(row.instrument).upperLimitPrice),
                                limitBuy: item.limitBuy / 100,
                                limitSize: item.limitSize,
                            });
                        }
                        else {
                            //
                        }
                    });
                });
            }
            else {
                console.error(resp);
            }
        }
        catch (ex) {
            //
        }
        finally {
            this.isProcessing = false;
        }
    }

    exportSome() {

        var date = new Date().format('yyyyMMdd');
        var time = new Date().format('hhmmss');
        this.tableObj.exportAllRecords(`${this.title}-${this.userInfo.userName}-${date}-${time}`);
    }

    autoFit() {
        
        this.registerEvent('auto-fit', () => { 
            this.tableObj.fitColumnWidth(); 
            this.tableObj2.fitColumnWidth(); 
        });
    }

    build($container) {

        super.build($container);
        this.createTable();
        this.createTable2();
        this.sortableRows();
        this.createToolbarApp();
        this.createSplitter();
        this.autoFit();
        setInterval(() => { this.updateFrontOrder(); }, 1000 * 3);
    }
};