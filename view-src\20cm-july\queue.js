const { IView } = require('../../component/iview');
const { TaskObject } = require('./objects');
const { repoInstrument } = require('../../repository/instrument');
const { SubscribeManager } = require('./subscribe-manager');
const { NumberMixin } = require('../../mixin/number');

module.exports = class QueueView extends IView {

    constructor() {

        super('@20cm-july/queue', false, '队列');

        /**
         * 合约/任务/订单委托数量，映射表（json map）
         * 1. 一层key：合约
         * 2. 二层key：task id
         * 3. value：多个委托单，委托数量
         * 两层map结构目的，在于避免前后任务为相同合约，造成在高亮的判断逻辑，存在可能串的问题
         */
        this.allOrdersMap = {};

        /** 远端涨停价，排队队列（剩余量） */
        this.leftRemotes = [];
        /** 远端涨停价，排队队列（已展示量） */
        this.remotes = [];
        this.firstScreenCount = 14 * 9;
        this.batchCount = 14 * 3;
        this.hasSubscribedFrontDataMap = {};
        this.isFrontCancelOn = this.app.frontCancel === true;

        this.states = {

            /** 当前选中的任务ID */
            taskId: null,
            /** 当前选中的任务，在已下单的前提下，对应的交易所订单号 */
            orderNo: null,
            /** 当前选中的任务合约 */
            instrument: null,
            /** 当前选中的任务合约名称 */
            instrumentName: null,
            buy1: null,
            ahead: null,
            aheadAmount: null,
            front_cancel: null,
            total: null,
            /** 涨停价 */
            ceil: null,
        };

        this.submgr = new SubscribeManager(this);
    }
    
    /**
     * @param {String} instrument
     */
    shortizeCode(instrument) {
        return instrument.split('.')[1];
    }

    createToolbar() {

        new Vue({

            el: this.$container.firstElementChild,
            data: {

                isFrontCancelOn: this.isFrontCancelOn,
                text: this.title,
                states: this.states,
                remotes: this.remotes,
            },
            mixins: [NumberMixin],
            methods: this.helper.fakeVueInsMethod(this, [

                this.loadMore,
                this.hasMatched,
                this.shortizeCode,
            ]),
        });
    }

    setAsInstrument(instrument, instrumentName, task_id, order_no) {

        const ref = this.states;
        const last = ref.instrument;

        // /**
        //  * 合约未变更，无需订阅
        //  */
        // if (instrument == last || this.helper.isNone(instrument) && this.helper.isNone(last)) {
        //     return;
        // }
        
        ref.taskId = task_id;
        ref.orderNo = order_no;
        ref.instrument = instrument;
        ref.instrumentName = instrumentName;
        ref.buy1 = null;
        ref.ahead = null;
        ref.aheadAmount = null;
        ref.front_cancel = null;
        ref.total = null;
        ref.ceil = null;

        this.resetQueue([]);
        this.requestTick(instrument);
        this.subscribeOrderQueue(instrument);
    }

    /**
     * @param {Array<TaskObject>} tasks 
     */
    mapOrders(tasks) {

        /**
         * 策略任务，由待买委托切换到已买委托后，会生成新的交易所订单号，需要同步更新该编号
         * 涨停队列使用该编号，映射其所处队列中的位置
         */
        const ref = this.states;
        const matchedTask = tasks.find(x => (x.id == ref.taskId || x.taskId == ref.taskId) && x.instrument == ref.instrument);
        if (matchedTask) {
            ref.orderNo = matchedTask.orderNo;
        }

        // 遍历监控任务，将下挂订单放到字典表里
        tasks.forEach(item => {

            let key_instrument = item.instrument;
            let key_task_id = item.id;

            // 合约层，下层为taskid层
            let level1 = this.allOrdersMap[key_instrument];
            if (level1 == undefined) {
                level1 = this.allOrdersMap[key_instrument] = {};
            }

            let dict1 = item.orderInfo || {};
            let list = [];

            for (let key in dict1) {

                let dict2 = dict1[key] || {};
                if (this.helper.isJson(dict2)) {

                    for (let key2 in dict2) {

                        let volume = dict2[key2];
                        if (typeof volume == 'number') {
                            list.push(Math.floor(volume / 100));
                        }
                    }
                }
            }

            level1[key_task_id] = list;
        });

        tasks.forEach(item => {
            
            if (this.hasSubscribedFrontDataMap[item.id] == undefined) {
                
                this.hasSubscribedFrontDataMap[item.id] = true;
                this.submgr.subscribe(item.id, this.systemTrdEnum.tickType.front, true);
            }
        });
    }

    /**
     * @param {Array<Number>} remotes 远端挂单
     */
    resetQueue(remotes) {

        this.states.total = remotes.length;
        var hands = remotes.map(x => Math.ceil(x * 0.01));

        if (hands.length <= this.firstScreenCount) {

            this.leftRemotes.clear();
            this.remotes.refill(hands);
        }
        else {
            
            this.remotes.refill(hands.splice(0, Math.max(this.firstScreenCount, this.remotes.length)));
            this.leftRemotes.refill(hands);
        }
    }

    async requestTick(instrument) {

        if (this.helper.isNone(instrument)) {
            return;
        }
        
        var resp = await repoInstrument.queryPrice(instrument);
        var { errorCode, errorMsg, data } = resp;
        var { upperLimitPrice } = data || {};

        if (errorCode == 0 && upperLimitPrice > 0) {
            this.states.ceil = upperLimitPrice;
        }
    }

    /**
     * @param {*} current 通过轮询方式，实现主动更新（订阅）排单对列
     */
    subscribeOrderQueue(current) {

        if (this.queueUpdateJob === undefined) {
            this.queueUpdateJob = setInterval(() => { this.requestQueue(); }, 500);
        }

        this.requestQueue();
    }

    async requestQueue() {

        if (this.isRequestingQueue === true) {
            return;
        }

        this.isRequestingQueue = true;
        let instrument = this.states.instrument;
        if (!instrument) {

            this.isRequestingQueue = false;
            return;
        }

        try {

            let resp = await repoInstrument.queryQueue([instrument]);
            let { errorCode, errorMsg, data } = resp;
            
            if (errorCode == 0 && this.helper.isJson(data)) {

                let matched = data[instrument];
                if (this.helper.isJson(matched)) {

                    let { volumes, orderNos } = matched;
                    this.handleQueueChange(instrument, volumes, orderNos);
                }
            }
            else {
                console.error('unexpect order queue result', resp);
            }
        }
        catch(ex) {
            console.error('order queue request error', ex);
        }
        finally {
            this.isRequestingQueue = false;
        }
    }

    /**
     * @param {String} message 
     */
    log(message) {
        this.loggerTrading.info('ztlog(n/a scenarios):' + message);
    }

    /**
     * @param {*} instrument 
     * @param {number[]} volumes 
     * @param {number[]} orderNos 
     */
    handleQueueChange(instrument, volumes, orderNos) {

        if (instrument != this.states.instrument) {
            return;
        }

        const ref = this.states;
        const is_empty = volumes.length == 0;

        if (is_empty) {
            this.log('order queue is empty/' + JSON.stringify({ instrument, orderNos: (orderNos || []).length }));
        }

        // 更新买1量（涨停封单量）
        ref.buy1 = is_empty ? null : Math.round(0.01 * volumes.sum());
        // 匹配订单位置
        let first_match_idx = orderNos.findIndex(x => x == ref.orderNo);
        // 是否有撞到相应订单
        let has_no_match = is_empty || first_match_idx == -1;
        // 累加开头至匹配位置的委托量
        ref.ahead = has_no_match ? null : 0.01 * volumes.slice(0, first_match_idx).sum();
        // 累加开头至匹配位置的委托金额
        ref.aheadAmount = has_no_match || typeof ref.ceil != 'number' ? null : ref.ahead * ref.ceil / 100; // 转换为万元，总量已经为手，除100即可
        // 更新委托笔数
        ref.total = volumes.length;
        this.resetQueue(volumes);
    }

    hasMatched(hands) {

        let { instrument, taskId } = this.states;

        if (this.helper.isNone(instrument) || this.helper.isNone(taskId)) {
            return false;
        }

        var volumes = (this.allOrdersMap[instrument] || {})[taskId];
        return volumes instanceof Array && volumes.indexOf(hands) >= 0;
    }

    loadMore() {

        if (this.leftRemotes.length > 0) {
            this.remotes.merge(this.leftRemotes.splice(0, this.batchCount));
        }
    }

    handleReconnect() {
        this.subscribeOrderQueue(this.states.instrument);
    }

    build($container) {

        super.build($container);
        this.createToolbar();
        this.renderProcess.on(this.systemEvent.tradingServerReestablished, () => { this.handleReconnect(); });
    }
};