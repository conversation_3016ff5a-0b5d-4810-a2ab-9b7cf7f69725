<div class="trade-view-block">

	<div class="view-toolbar tabbed-radios-external s-pdl-10 s-pdr-10">
		
		<el-radio-group size="mini" v-model="states.focused" @change="handleTabChange">
			<el-radio-button :label="tabs.entrust">委托</el-radio-button>
			<el-radio-button :label="tabs.position">持仓</el-radio-button>
		</el-radio-group>

		<template v-if="states.focused == tabs.entrust">
			<el-button type="primary" size="mini" class="s-mgl-20" style="height: 18px;" @click="refreshOrders">
				<i class="el-icon-refresh"></i>
				<span>刷新委托</span>
			</el-button>
			<el-button type="warning" size="mini" class="s-mgl-20"  style="height: 18px;" @click="cancelChecks">撤勾选</el-button>
			<el-button type="danger" size="mini" class="s-mgl-20"  style="height: 18px;" @click="cancelAll">撤全部</el-button>
			<el-checkbox class="s-mgl-20" v-model="states.onlyCancellable" @change="handleFilterChange">仅显示可撤</el-checkbox>
			<el-input placeholder="搜索委托" class="s-mgl-10 s-w-120" v-model.trim="states.keywords.order" @change="handleFilterChange" clearable></el-input>
		</template>

		<template v-else>
			<el-button type="primary" size="mini" class="s-mgl-20" style="height: 18px;" @click="refreshPositions">
				<i class="el-icon-refresh"></i>
				<span>刷新持仓</span>
			</el-button>
			<el-input placeholder="搜索持仓" class="s-mgl-10 s-w-120" v-model.trim="states.keywords.position" @change="filterPositions" clearable></el-input>
		</template>

		<span class="fixed-right-icon">
			<el-tooltip v-if="states.isIntegrated" content="弹出">
				<a class="iconfont icon-expand s-fs-18" @click="popout"></a>
			</el-tooltip>
			<el-tooltip v-else content="列表全占 | 共享">
				<a class="iconfont icon-expand s-fs-18" @click="occupy"></a>
			</el-tooltip>
		</span>

	</div>

    <div class="table-order s-full-height">
		<table>
			<tr>
				<th type="check" fixed-width="40"></th>
				<th label="证券代码" prop="instrument" min-width="100" formatter="shortizeCodeCol" searchable sortable overflowt></th>
				<th label="证券名称" prop="instrumentName" min-width="100" searchable sortable overflowt></th>
				<th label="委托金额" prop="orderAmount" min-width="100" align="right" thousands-int sortable overflowt></th>
				<th label="委托数量" prop="volumeOriginal" min-width="100" align="right" thousands-int sortable overflowt></th>
				<th label="成交" prop="tradedVolume" min-width="100" align="right" thousands-int sortable overflowt></th>
				<th label="委托时间" prop="orderTime" min-width="100" formatter="formatTime" sortable overflowt></th>
				<th label="委托价格" prop="orderPrice" min-width="100" align="right" formatter="formatPrice" sortable overflowt></th>
				<th label="成交价格" prop="tradedPrice" min-width="100" align="right" formatter="formatPrice" sortable overflowt></th>

				<th label="状态" 
					prop="orderStatus" 
					min-width="70" 
					watch="orderStatus, errorMsg" 
					align="right" 
					formatter="formatOrderStatus" 
					export-formatter="formatOrderStatusText" 
					sortable
					overflowt></th>

				<th label="方向" 
					prop="direction" 
					min-width="70" 
					align="right" 
					formatter="formatDirection" 
					export-formatter="formatDirectionText" 
					sortable
					overflowt></th>

				<th label="交易方式" 
					min-width="100"
					prop="businessFlag"
					formatter="formatBusinessFlag" 
					export-formatter="formatBusinessFlag" 
					sortable></th>

				<th label="成交时间" prop="tradeTime" min-width="70" formatter="formatTime"></th>
				<th label="报单编号" prop="exchangeOrderId" min-width="130" searchable sortable overflowt></th>
				<th label="错误信息" min-width="150" prop="errorMsg" overflowt sortable></th>
				<th label="操作" prop="isCompleted" fixed="right" fixed-width="60" formatter="formatActions" exportable="false"></th>

			</tr>
		</table>
	</div>

    <div class="table-position s-full-height">
		<table>
			<tr>
				<th label="证券代码" min-width="100" prop="instrument" formatter="shortizeCodeCol" searchable sortable overflowt></th>
				<th label="证券名称" min-width="80" prop="instrumentName" searchable sortable overflowt></th>
				<th label="持仓数量" min-width="90" prop="totalPosition" align="right" thousands-int sortable overflowt></th>
				<th label="可用数量" min-width="90" prop="closableVolume" align="right" thousands-int sortable overflowt></th>
				<th label="持仓均价" min-width="80" prop="weightedPrice" align="right" formatter="formatPrice" sortable overflowt></th>
				<th label="市值" min-width="110" prop="marketValue" align="right" thousands sortable overflowt></th>
				<th label="盈亏" min-width="110" prop="profit" align="right" class-maker="makeBenefitClass" thousands sortable overflowt></th>
				<th label="盈亏比例" min-width="110" prop="profitPct" watch="accountTotalAsset" align="right" class-maker="makeBenefitClass" percentage by100 sortable overflowt></th>
			</tr>
		</table>
	</div>

</div>