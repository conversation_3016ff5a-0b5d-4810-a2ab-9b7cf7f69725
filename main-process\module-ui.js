﻿/**
 * UI manager
 */

const electron = require('electron');
const remote = require('@electron/remote/main');
const BrowserWindow = electron.BrowserWindow;
const path = require('path');
const fs = require('fs');
const Routing = require('../model/routing').Routing;
const ServerEnvMainModule = require('./main-module').ServerEnvMainModule;
const { ConvertPinyin } = require('../libs/3rd/pinyin-converter');
const { LocalSetting } = require('../config/system-setting.local');

class UIModule extends ServerEnvMainModule {

    /**
     * @param {String} module_name 
     * @param {Array<Routing>} routings 
     */
    constructor(module_name, routings) {

        super(module_name);
        this.routings = routings;
    }

    createSignInWindow() {

        var isByUser = this.app.GeneralSettings.signin == 'user';
        var signInPage = isByUser ? '@sign-in-user' : '@sign-in-account';

        this.mainProcess.emit(this.systemEvent.huntWinLandscapeFromMain, signInPage, { width: 368, height: 510, maximizable: false, resizable: false }, the_win => {

            // will never happen (just 4 code intelligence)
            if (!(the_win instanceof BrowserWindow)) {
                return;
            }

            this.signInWindow = the_win;
            this.signInWindow.on('closed', () => {
                this.exitApp();
            });
            this.setContextDataItem(this.dataKey.loginWindowId, this.signInWindow.id);
        });
    }

    directUser2SigninWindow() {

        try {
            this.closNotSignInWindows();
            this.signInWindow.show();
        } 
        catch (ex) {

            this.loggerConsole.fatal(ex.message + ' > ' + ex.stack);
            this.loggerSys.fatal(ex.message);
        }
    }

    sendNetworkStatusMessage(message) {
        
        var central_window = this.centralWindow;
        central_window && central_window.webContents.send(this.systemEvent.networkStatusChange, message);
    }

    listen2NetworkConnChange() {

        this.tradingServer.listen2Event(this.systemEvent.connEstablished, event => {
            this.sendNetworkStatusMessage({ ok: true, content: '交易服务已连接' });
        });

        this.tradingServer.listen2Event(this.systemEvent.connClosed, event => {
            this.sendNetworkStatusMessage({ ok: false, content: '交易服务已断开' });
            this.isDisconnectedByLogout ? this.directUser2SigninWindow() : null;
        });

        this.tradingServer.listen2Event(this.serverEvent.forcedKickOut, event => {
            this.doLogout();
            this.signInWindow.webContents.send(this.serverEvent.forcedKickOut.toString());
        });

        var require_quote_server = !this.userInfo ? false : !this.userInfo.isSuperAdmin;
        if (require_quote_server) {

            this.quoteServer.listen2Event(this.systemEvent.connEstablished, event => {
                this.sendNetworkStatusMessage({ ok: true, content: '行情服务已连接' });
            });

            this.quoteServer.listen2Event(this.systemEvent.connClosed, event => {
                this.sendNetworkStatusMessage({ ok: false, content: '行情服务已断开' });
            });
        }
    }

    shouldExit() {
        return !this.isDisconnectedByLogout && !this.isDisconnectedTradingServerAccidently && !this.isDisconnectedQuoteServerAccidently;
    }

    setCentralWindowId2Context(win_id) {
        this.setContextDataItem(this.dataKey.centralWindowId, win_id);
    }

    createMainWindow() {

        this.dashboardWindow = new BrowserWindow({
                
            width: 1366,
            minWidth: 1000,
            height: 738,
            minHeight: 700,
            show: true,
            frame: false,
            webPreferences: {
                webviewTag: true,
                nodeIntegration: true,
                contextIsolation: false,
            }
        });

        // 设置当前窗口对Render Process可见
        remote.enable(this.dashboardWindow.webContents);
        this.dashboardWindow.loadURL(`file://${path.join(__dirname, '../component/win-dashboard.html')}`);
        this.equipMainWindow();
    }

    equipMainWindow() {

        this.dashboardWindow.on('closed', () => {

            this.dashboardWindow = null;
            this.setCentralWindowId2Context(null);
            if (this.shouldExit()) {
                this.exitApp();
            }
        });

        this.setCentralWindowId2Context(this.dashboardWindow.id);
        this.dashboardWindow.on(this.systemEvent.renderWindowPrepared, () => {
            this.dashboardWindow.emit(this.systemEvent.globalViewRouting, this.routings);
        });
    }

    handleSuccessfulLogin(event) {

        // hide sign-in window anyway
        this.signInWindow.hide();

        // create welcome ( data preparing ) window
        this.mainProcess.emit(this.systemEvent.huntWinLandscapeFromMain, '@welcome', { width: 450, height: 270, maximizable: false, resizable: false }, the_win => {

            // will never happen (just 4 code intelligence)
            if (!(the_win instanceof BrowserWindow)) {
                return;
            }

            this.welcomeWindow = the_win;
            this.welcomeWindow.on('closed', () => {
                this.welcomeWindow = null;
                // 在系统准备完毕之前，欢迎窗口被人为关闭时，软件将退出
                if (!this.sysPrepaingCompleted) {
                    this.exitApp();
                }
            });
        });

        this.arrangeStockReUpdate();
    }

    /**
     * 股票合约数据强制再更新
     */
    arrangeStockReUpdate() {
        
        let now = new Date();
        let now_ts = now.getTime();
        let today_0900_ts = new Date(now.format('yyyy-MM-dd 09:00:00')).getTime();
        let data_key = 'stock-reupdate-required';

        if (now_ts >= today_0900_ts) {

            // 在9点后登录，获取的合约已经是最新版
            this.setContextDataItem(data_key, 0);
        }
        else {

            // 在9点前登录，于9点后，安排一次股票合约的再更新
            this.setContextDataItem(data_key, 1);
            let delay = Math.max(1000, today_0900_ts - now_ts);
            let stock_file_path = LocalSetting.getMarketStockPath();
            setTimeout(() => { this.requestStocks(stock_file_path); }, delay);
        }
    }

    async requestStocks(filePath) {

        let start = Date.now();
        // 延迟加载模块（服务器地址，需登录成功后，才能确定）
        let { repoInstrument } = require('../repository/instrument');
        let response = await repoInstrument.getAll(this.systemEnum.assetsType.stock.code);
        let { errorCode, errorMsg, data } = response;
        let stocks = Array.isArray(data) ? data : [];

        if (errorCode !== 0) {

            this.loggerSys.error(`stock list re-update with error:${errorCode}/${errorMsg}`);
            return;
        }

        stocks.forEach(ins => {

            // 底层合约数据可能在该字段传0，在底层数据确保无误后，该逻辑可删除
            if (ins.priceTick === 0) {
                ins.priceTick = 0.01;
            }

            ins.py = ConvertPinyin(ins.instrumentName, true);
            ins.pricePrecision = this._calculatePricePrecision(ins.priceTick);
        });
        
        let end = Date.now();
        fs.writeFileSync(filePath, JSON.stringify(stocks), 'utf8');
        this.loggerSys.info(`stock list re-update finished, cost = ${end - start}, file = ${filePath}`);
    }

    _calculatePricePrecision(price_tick) {

        if (typeof price_tick == 'number' || typeof price_tick == 'string') {

            let str = price_tick.toString();
            return str.indexOf('.') < 0 ? 0 : str.split('.')[1].length;
        }
        else {
            return 2;
        }
    }

    closNotSignInWindows() {

        var all_windows = this.electron.BrowserWindow.getAllWindows();
        var signin_win_id = this.signInWindow.id;

        all_windows.forEach(the_win => {
            
            if (the_win.id == signin_win_id || !the_win.isVisible()) {
                return;
            }
            else {
                the_win.close();
            }
        });
    }

    doLogout() {

        this.setContextDataItem(this.dataKey.disconnectedByLogout, true);
        this.closNotSignInWindows();

        try {
            this.tradingServer.logout();
        }
        catch (ex) {
            
            this.loggerConsole.fatal(ex.message + ' > ' + ex.stack);
            this.loggerSys.fatal(ex.message);
        }

        try {
            this.quoteServer.logout();
        } 
        catch (ex) {
            
            this.loggerConsole.fatal(ex.message + ' > ' + ex.stack);
            this.loggerSys.fatal(ex.message);
        }

        // user asks to log out, the system goes back to sign-in window
        this.signInWindow.show();
    }

    sendPongMessage(message) {

        var central_window = this.centralWindow;
        central_window && central_window.webContents.send(this.systemEvent.pingPong, message);
    }

    listen2Events() {

        // 创建第一个窗口 --  登录窗口
        this.mainProcess.once('to-create-first-window', this.createSignInWindow.bind(this));

        // 登录成功（包括交易服务器、行情服务器，综合判断为登录成功）
        this.mainProcess.on(this.systemEvent.loginRequestValidatedOk, this.handleSuccessfulLogin.bind(this));

        // 欢迎窗口，完成必备数据加载
        this.mainProcess.on(this.systemEvent.sysLoadingCompleted, event => {

            this.sysPrepaingCompleted = true;
            this.tradingServer.start2Ping(this.sendPongMessage.bind(this));
            this.listen2NetworkConnChange();
            this.createMainWindow();
        });

        // 主窗口已准备完毕
        this.mainProcess.on(this.systemEvent.mainWindowReady, event => {

            if (this.welcomeWindow) {
                this.welcomeWindow.close();
            }
            this.dashboardWindow.show();
        });

        /**
         * 注销的两种情况：
         * 1. 用户主动注销
         * 2. 管理员强制用户下线
         */
        this.mainProcess.on(this.systemEvent.toLogout, event => {
            delete this.sysPrepaingCompleted;
            this.doLogout();
        });
    }

    checkSingleInstance() {

        var got_locked = this.app.requestSingleInstanceLock();

        this.app.on('second-instance', (cmd_line, working_dir) => {

            if (this.dashboardWindow) {

                this.dashboardWindow.isMinimized() ? this.dashboardWindow.restore() : null;
                this.dashboardWindow.focus();
            } 
            else if (this.welcomeWindow) {

                this.welcomeWindow.isMinimized() ? this.welcomeWindow.restore() : null;
                this.welcomeWindow.focus();
            } 
            else if (this.signInWindow) {

                this.signInWindow.isMinimized() ? this.signInWindow.restore() : null;
                this.signInWindow.focus();
            }
        });

        if (!got_locked) {
            this.exitApp();
        }
    }

    /**
     * 删除旧日志文件
     */
    deleteElderLogFiles() {
        
        const log_folder = process.platform == 'darwin' ? '/tmp/logs' : path.join(process.cwd(), 'logs');
        
        if (!fs.existsSync(log_folder)) {
            return;
        }

        const state_folder = fs.statSync(log_folder);
        if (!state_folder.isDirectory()) {
            return;
        }

        const assets = fs.readdirSync(log_folder);
        const one_day_ms = 24 * 60 * 60 * 1000;
        const now_ts = Date.now();
        const few_days_ago = new Date(now_ts - one_day_ms * 7).format('yyyy-MM-dd');
        // 数天前的日志文件
        const few_days_ago_p1 = `log-${few_days_ago}.log`;
        // 数天前的HTTP接口日志文件
        const few_days_ago_p2 = `log-${few_days_ago}-http.log`;
        
        assets.forEach(fname => {

            try {

                let full_path = path.join(log_folder, fname);
                let state = fs.statSync(full_path);
                if (!state || !state.isFile()) {
                    return;
                }

                if (fname.endsWith('-http.log') && fname <= few_days_ago_p2) {
                    fs.unlinkSync(full_path);
                }
                else if (fname.endsWith('.log') && fname <= few_days_ago_p1) {
                    fs.unlinkSync(full_path);
                }
            }
            catch (ex) {
                console.error(ex);
            }
        });
    }

    exitApp() {
        this.app.quit();
    }

    run() {
        
        // this.checkSingleInstance();
        this.deleteElderLogFiles();
        this.listen2Events();
    }
}

module.exports = { UIModule };
