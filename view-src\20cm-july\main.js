const { IView } = require('../../component/iview');
const { RecordsView, AggregatedPosition } = require('./main/records');
const { ZtSetting, TaskObject } = require('../20cm-july/objects');
const { WinPosSizeMgr } = require('../../toolset/win-pos-size-mgr');
const { Cm20FunctionCodes } = require('../../config/20cm');
const { AccountSummaryInfo } = require('./model/objects');

module.exports = class MainView extends IView {

    constructor(isIntegrated) {

        super('@20cm-july/main', false, '手动与自动交易');
        this.isIntegrated = isIntegrated === true;
        this.renderProcess.on('push-sells', (event, sells) => { this.handleProcessPush(sells); });
        this.renderProcess.on('push-account-summary', (event, summarySh, summarySz) => {
            this.attachAccounts(summarySh, summarySz); 
        });

        if (!this.isIntegrated) {
            this.manageWindow();
        }
    }

    manageWindow() {

        this.winMgr = new WinPosSizeMgr('zt_20cm_popout_records', this.thisWindow);
        var data = this.winMgr.getLast();
        if (data) {
            this.winMgr.recover();
        }
        else {
            this.thisWindow.setSize(552, 432);
        }
        
        this.winMgr.abserve();
    }

    /**
     * @param {String} message 
     */
    log(message) {
        this.loggerTrading.info('ztlog:' + message);
    }

    buildLayout() {

        var $topRoot = this.$container.querySelector('.part-top');
        var $level2Root = this.$container.querySelector('.part-bottom .view-level2');
        var $tradingRoot = this.$container.querySelector('.part-bottom .view-trading');
        var $sellTask = this.$container.querySelector('.part-bottom .view-sell-task-ext');

        /**
         * 上方，持仓与委托
         */
        var vrecords = new RecordsView();
        vrecords.loadBuild($topRoot);
        vrecords.registerEvent('popout', () => { this.trigger('popout'); });
        vrecords.registerEvent('hit-position', this.handlePositionPunch.bind(this));
        vrecords.registerEvent('cared-position-update', this.handleCaredPositionUpdate.bind(this));
        vrecords.registerEvent('position-update', this.handlePositionUpdate.bind(this));
        vrecords.registerEvent('occupy-change', this.handleOccupyChange.bind(this));
        setTimeout(() => { vrecords.states.isIntegrated = this.isIntegrated; }, 2000);
        this.vrecords = vrecords;

        /**
         * 下左，行情
         */
        const Level2View = require('./main/level2');
        var vlevel2 = new Level2View();
        vlevel2.loadBuild($level2Root);
        vlevel2.registerEvent('level-selected', this.handleLevelSelect.bind(this));
        vlevel2.registerEvent('cage-price-change', this.handleCagePriceChange.bind(this));
        this.vlevel2 = vlevel2;

        /**
         * 下中，交易面板
         */
        const TradingView = require('./main/trading');
        var vtrading = new TradingView();
        vtrading.loadBuild($tradingRoot);
        vtrading.registerEvent('request-position', this.handlePositionRequest.bind(this));
        vtrading.registerEvent('set-instrument', this.handleTradingInstrumentChange.bind(this));
        this.vtrading = vtrading;

        /**
         * 下右，卖出策略列表
         */
        const SellTaskView = require('./main/sell-task');
        var vsell = new SellTaskView();
        vsell.loadBuild($sellTask);
        vsell.registerEvent('hit-sell-task', this.handleSellTaskHit.bind(this));
        this.vsell = vsell;
    }

    handleProcessPush(sells) {
        this.push(sells);
    }

    /**
     * @param {Array<TaskObject>} tsells 
     */
    push(tsells) {

        this.vtrading.push(this.helper.deepClone(tsells));
        this.vsell.push(this.helper.deepClone(tsells));
    }

    setAsRington(rington) {
        
        if (this.vrecords) {
            this.vrecords.setAsRington(rington);
        }
        else {
            setTimeout(() => { this.vrecords.setAsRington(rington); }, 1000 * 3);
        }
    }

    /**
     * @param {ZtSetting} setting 
     */
    setAsSetting(setting) {
        
        if (this.vrecords) {
            this.vrecords.setAsSetting(setting);
        }
        if (this.vtrading) {
            this.vtrading.setAsSetting(setting);
        }
        else {
            setTimeout(() => { 
              this.vrecords.setAsSetting(setting);
              this.vtrading.setAsSetting(setting);
            }, 1000 * 3);
        }
    }

    handleTradingInstrumentChange(instrument, instrumentName) {
        
        this.vlevel2.setAsInstrument(instrument, instrumentName);
        this.trigger('inst-change', instrument, instrumentName);
    }

    /**
     * @param {TaskObject} task 
     */
    handleSellTaskHit(task) {
        this.vtrading.handleSellTask(task);
    }

    keepAligned(instrument, instrumentName) {
        
        this.log(`main trading panel is notified with stock change: ${instrument}/${instrumentName}`);
        this.vlevel2.setAsInstrument(instrument, instrumentName);
        this.vtrading.setAsInstrument(instrument, instrumentName);
    }

    /**
     * @param {AccountSummaryInfo} summarySh 
     * @param {AccountSummaryInfo} summarySz 
     */
    attachAccounts(summarySh, summarySz) {

        if (this.vrecords) {
            this.vrecords.attachAccounts(summarySh, summarySz);
        }
        else {
            setTimeout(() => { this.vrecords.attachAccounts(summarySh, summarySz); }, 1000 * 1);
        }
    }

    /**
     * @param {AggregatedPosition} position 
     */
    handlePositionPunch(position) {

        var { instrument, instrumentName } = position;
        this.log(`main trading panel is notified with position hit: ${instrument}/${instrumentName}`);
        this.vlevel2.setAsInstrument(instrument, instrumentName);
        this.vtrading.setPositionByExternal(position);
        this.vtrading.setAsPrice(this.vlevel2.getLastPrice());
        this.trigger('inst-change', instrument, instrumentName);
    }

    handleOccupyChange(occupied) {

        var $node = this.$container.querySelector('.view-main-inter');
        var clsname = 'full-occupied';
        
        if (occupied) {
            $node.classList.add(clsname);
        }
        else {
            $node.classList.remove(clsname);
        }
    }

    handleCagePriceChange(lowerSellLimit, upperBuyLimit) {

        this.vtrading.mannual.lowerSellLimit = Math.max(lowerSellLimit, this.vtrading.mannual.floor) || 0;

        // 第一次加载时合约信息可能还没返回，没有涨停价，会抹掉upperBuyLimit
        if (this.vtrading.mannual.ceiling > 0) {
            this.vtrading.mannual.upperBuyLimit = Math.min(upperBuyLimit, this.vtrading.mannual.ceiling) || 0;            
            
        } else {
            this.vtrading.mannual.upperBuyLimit = upperBuyLimit || 0;
        }
    }

    handleLevelSelect(price) {

        this.log(`main trading panel is notified with level select(price): ${price}`);
        this.vtrading.mannual.price = price || 0;
    }

    handlePositionRequest(instrument) {

        var matched = this.vrecords.findPosition(instrument);
        this.log(`main panel requested position, on behalf of trading panel: ${instrument}, position: ${JSON.stringify(matched)}`);
        this.vtrading.setPositionByRequest(matched);
    }

    /**
     * @param {AggregatedPosition} position 
     */
    handleCaredPositionUpdate(position) {

        this.log(`main panel is asked to deliever a cared position update: ${JSON.stringify(position)}`);
        this.vtrading.hotUpdatePosition(position);
    }

    handlePositionUpdate(data) {
        this.trigger('position-update', data);
    }

    handleTasks(records) {

        var sells = records.filter(task => task.direction == this.systemTrdEnum.tradingDirection.sell.code);
        if (sells.length == 0) {
            return;
        }
        
        this.log(`stand-along main window is to push sells: ${JSON.stringify(sells)}`);
        this.push(sells);
    }

    requestTasks() {

        if (this.hasListened == undefined) {

            this.hasListened = true;
            this.renderProcess.on(Cm20FunctionCodes.reply.queried.toString(), (event, { data, errorCode, errorMsg }, reqId) => {
                errorCode == 0 && this.handleTasks(data, reqId);
            });
        }

        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.query, {});
    }

    build($container) {

        super.build($container);
        this.buildLayout();
        
        if (!this.isIntegrated) {

            this.$container.classList.add('popout-trading-window');
            this.requestTasks();
            window.vmain = this;
        }
    }
};