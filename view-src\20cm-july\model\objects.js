class AccountSummaryInfo {

    constructor({ balance, preBalance, available, equity, credit }) {

        this.balance = balance || 0;
        this.preBalance = preBalance || 0;
        this.available = available || 0;
        this.equity = equity || 0;
        this.credit = credit || 0;
    }

    static CreateEmptySummary() {
        return new AccountSummaryInfo({});
    }
}

module.exports = {
    AccountSummaryInfo,
};