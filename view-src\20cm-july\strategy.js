const { IView } = require('../../component/iview');
const { AccountSimple } = require('../20cm/components/objects');
const { ZtStandardStrategy, CriteriaParam, BuyTask, TaskObject, ZtSetting, BuyCancelTrigger, Entrance, mergeLocalAndRemoteStrategies } = require('./objects');
const { TriggerHandlers } = require('./trigger-handler');
const { BizHelper } = require('../../libs/helper-biz');
const { repoAccount } = require('../../repository/account');
const { repoInstrument } = require('../../repository/instrument');
const { repo20Cm } = require('../../repository/20cm');

module.exports = class StrategyView extends IView {

    constructor() {

        super('@20cm-july/strategy', false, '交易策略');
        this.accounts = [new AccountSimple({})].splice(1);
        this.tmpStrategyName = '临时策略';
        this.strategies = [''].splice(1);
        this.settings = [this.createTemprary()];
        this.figures = [];
        this.devides = [1, 2, 3, 4, 5, 10];
        this.by = { money: 5, ratio: 6 };
    }

    createTemprary() {

        let schema = new ZtStandardStrategy(this.tmpStrategyName, true);
        let triggers = TaskObject.transerTriggers(this.getContextDataItem(this.dataKey.logInInput));
        triggers.buys.forEach(item => { schema.btriggers.push(item); });
        triggers.cancels.forEach(item => { schema.ctriggers.push(item); });
        return schema;
    }

    /**
     * @param {String} instrument
     */
    shortizeCode(instrument) {
        return instrument.split('.')[1];
    }
    
    createToolbar() {

        this.states = {

            choosed: this.settings[0].name,
            current: this.typeds(this.helper.deepClone(this.settings[0])),
            saving: { visible: false, name: null },
            taskInfo: {

                instrument: null,
                instrumentName: null,
                status: null,
            },
        };

        new Vue({

            el: this.$container.firstElementChild,
            data: {

                accounts: this.accounts,
                strategies: this.strategies,
                states: this.states,
                figures: this.figures,
                devides: this.devides,
                by: this.by,
            },
            computed: {

                current: () => {
                    return this.states.current;
                }
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.isByMoney,
                this.saveAll,
                this.saveAs,
                this.remove,
                this.startTask,
                this.stopTask,
                this.cancelTask,
                this.reset,
                this.canStart,
                this.canStop,
                this.canCancel,
                this.handleParamChange,
                this.handleLimitChange,
                this.handlePctShortcutClick,
                this.handleSwitched,
                this.canShowByName,
                this.showTotalCash,
                this.intoAmount,
                this.shortizeCode,
                this.hasAnyCreditAccount,
                this.isInstrumentCreditableBuy,
                this.isChoiceMode,
                this.handleInputCdtChange,
            ]),
        });
    }

    /**
     * @param {ZtStandardStrategy} setting 
     */
    isByMoney(setting) {
        return setting.limit.position.method == this.by.money;
    }

    /**
     * @param {ZtStandardStrategy} setting 
     */
    isByRatio(setting) {
        return !this.isByMoney(setting);
    }

    /**
     * 根据合约（所属市场）过滤其适配的下单账号
     */
    filterAccountsByMarket() {
        
        const all = this.accounts;
        const ins = this.states.taskInfo.instrument || '';
        const sh = 'SHSE';
        const sz = 'SZSE';

        if (!ins) {
            return all;
        }
        else {
            return all.filter(x => ins.indexOf(sh) >= 0 && x.market == sh || ins.indexOf(sz) >= 0 && x.market == sz);
        }
    }

    /**
     * @param {Array<ZtStandardStrategy>} strategies 
     */
    mergeLocalSetting(strategies) {

        const settings = TaskObject.transerTriggers(this.getContextDataItem(this.dataKey.logInInput));
        const { buys, cancels } = settings;

        strategies.forEach(strag => {
            mergeLocalAndRemoteStrategies(strag, this.helper.deepClone(buys), this.helper.deepClone(cancels));
        });
    }

    /**
     * 对用户配置进行格式化及兼容处理
     * @param {*} data 
     * @returns {{ strategies: ZtStandardStrategy[], setting: ZtSetting, rington: Object }}
     */
    compileConfig(data) {

        const schema = {

            strategies: [].map(x => this.typeds(x)),
            setting: ZtSetting.makeDefault(),
            rington: Entrance.makeDefaultRingtone(),
        };

        try {

            const settings_str = (data || {}).settings;
            const config = JSON.parse(settings_str || '{}');
            const { strategies, setting, rington } = config;
            
            this.helper.deepAssign(schema, {

                strategies: Array.isArray(strategies) ? strategies : [],
                setting: this.helper.isJson(setting) ? setting : ZtSetting.makeDefault(),
                rington: this.helper.isJson(rington) ? rington : Entrance.makeDefaultRingtone(),
            });

            this.log('user config json parse ok');
        }
        catch(ex) {

            console.error(data, ex);
            this.logFatal(`user config json parse error/${ex.message}/${JSON.stringify(data)}`);
        }

        /**
         * 将远端配置与本地配置进行合并
         */

        this.mergeLocalSetting(schema.strategies);

        /**
         * 对远端的交易限制结构进行检查，并补充缺失的属性
         */

        const tempStrategy = this.createTemprary();
        schema.strategies.forEach(item => {

            for (let key in tempStrategy.limit) {
                if (item.limit[key] == undefined) {
                    item.limit[key] = this.helper.deepClone(tempStrategy.limit[key]);
                }
            }
        });

        /**
         * 添加临时策略到开头
         */

        schema.strategies.unshift(this.createTemprary());
        return schema;
    }

    async requestSettings() {

        const resp = await repo20Cm.querySetting();
        const { data, errorCode, errorMsg } = resp;

        if (errorCode != 0) {

            this.interaction.showError(`用户配置查询失败（包含策略、快捷键、铃音）：${errorCode}/${errorMsg}`);
            this.logFatal('user config query error/' + JSON.stringify(resp));
        }

        const { strategies, setting, rington } = this.compileConfig(data);
        this.settings.refill(strategies);
        this.strategies.refill(strategies.map(x => x.name));
        
        /** 当有用户策略时，将用户策略的首个，设为默认（第一个策略为临时策略） */
        const focused = strategies.length >= 2 ? strategies[1] : strategies[0];
        this.states.choosed = focused.name;
        this.states.current = this.helper.deepClone(focused);
        this.log(`requested user strategies, and set focused as: ${JSON.stringify(focused)}`);
        // 上报用户全量的配置
        this.trigger('report-user-config', strategies, setting, rington);
    }

    saveAll() {
        
        var check_results = this.settings.filter(x => !x.isTemp).map(x => this.validate(x, this.states.current));
        var failed = check_results.find(x => !x.isOk);
        if (failed) {
            return this.interaction.showError(`${failed.setting.name} > ${failed.message}`);
        }

        /**
         * 将当前（可能存在）的改动，反向写入设置
         */
        var matched = this.settings.find(x => x.name == this.states.choosed);
        this.helper.extend(matched, this.helper.deepClone(this.states.current));
        this.log(`user save all strategies: names = ${this.settings.map(x => x.name).join(',')}`);

        /**
         * 执行保存
         */
        this.execSave();
    }

    /**
     * @param {ZtStandardStrategy} setting 
     */
    hasAnyInputError(setting) {

        let result = { isOk: true, error: '' };
        let { btriggers, ctriggers } = setting;
        let error_buy = btriggers.find(x => x.checked && x.conditions.some(y => !(y.threshold > 0)));
        let error_cancel = ctriggers.find(x => x.checked && x.conditions.some(y => !(y.threshold > 0)));

        if (error_buy) {

            result.isOk = false;
            result.error = error_buy.name;
        }
        else if (error_cancel) {

            result.isOk = false;
            result.error = error_cancel.name;
        }

        if (result.isOk) {

            /**
             * 买卖策略参数均验证通过时，再对规模限制参数进行检查
             */

            Object.assign(result, this.checkLimit(setting));
        }
        
        return result;
    }

    /**
     * @param {ZtStandardStrategy} setting 
     */
    checkLimit(setting) {

        let isOk = true;
        let error = '';
        const ref = setting.limit.position;

        if (ref.method == this.by.money && !(ref.amount > 0)) {

            isOk = false;
            error = '买入金额未指定';
        }
        else if (ref.method == this.by.ratio && !(ref.percentage > 0 && ref.percentage <= 100)) {

            isOk = false;
            error = '买入比例不为0-100%';
        }

        return { isOk, error };
    }

    saveAs() {

        const ref = this.states.saving;

        if (this.helper.isNone(this.states.choosed)) {
            return this.interaction.showError('貌似没有（选中）策略哦！');
        }
        else if (!ref.name) {
            return this.interaction.showError('请给一个名称吧！');
        }
        else if (ref.name.length > 16) {
            return this.interaction.showError('策略名称最多16个字符！');
        }
        else if (this.settings.some(x => x.name == ref.name)) {
            return this.interaction.showError('该名称已存在');
        }

        var cloned = this.typeds(this.helper.deepClone(this.states.current));
        cloned.name = ref.name;
        cloned.isTemp = false;

        let satisfy = this.hasAnyInputError(cloned);
        if (!satisfy.isOk) {
            return this.interaction.showError('策略参数项错误：' + satisfy.error);
        }

        this.log(`user save as a strategy: ${ref.name}`);
        this.settings.push(cloned);
        this.strategies.push(ref.name);
        ref.name = null;
        ref.visible = false;
        this.execSave();
    }

    /**
     * 上报策略配置变化，在外部进行各种设置的全量合并保存
     */
    execSave() {
        this.trigger('report-user-strategy', this.settings);
    }

    remove() {

        if (this.strategies.length > 1 && this.states.choosed == this.tmpStrategyName) {

            /**
             * 有用户策略时，临时策略不展示，如果临时策略处于选中状态，则不执行操作
             */
            return;
        }
        else if (this.helper.isNone(this.states.choosed)) {
            return this.interaction.showError('貌似没有（选中）策略哦！');
        }

        var ref = this.settings;
        var matched = ref.find(x => x.name == this.states.choosed);
        if (matched.isTemp) {
            return this.interaction.showError(this.tmpStrategyName + '，不能删除！');
        }

        this.interaction.showConfirm({

            title: '策略删除确认',
            message: `删除策略 / ${matched.name}，是否确定？`,
            confirmed: () => {

                this.log(`user delete one strategy: ${matched.name}`);
                this.strategies.remove(x => x == matched.name);
                ref.remove(x => x.name == matched.name);
                /** 尚具有用户策略时，选中第一个用户策略 */
                this.states.choosed = ref.length > 1 ? ref[1].name : ref[0].name;
                this.handleSwitched();
                
                /**
                 * 提交实际的策略设置
                 */
                this.execSave();
            },
        });
    }

    handleSwitched() {

        var matched = this.settings.find(x => x.name == this.states.choosed);
        this.states.current = this.helper.deepClone(matched);
        this.log(`user switch to another strategy: ${JSON.stringify(this.states.current)}`);
        this.trigger('switched', this.helper.deepClone(matched));
        setTimeout(() => { this.recalculateFigure(); }, 30);
    }

    handleParamChange() {

        this.log(`strategy param hot changed: ${JSON.stringify(this.states.current)}`);
        this.trigger('param-updated', this.helper.deepClone(this.states.current));
    }

    handleLimitChange() {

        this.handleParamChange();
        setTimeout(() => { this.recalculateFigure(); }, 30);
    }

    handlePctShortcutClick(shares) {
        
        this.states.current.limit.position.percentage = Math.round(100 /shares);
        this.handleLimitChange();
    }

    canShowByName(name, name_idx) {
        return this.strategies.length == 1 || name != this.tmpStrategyName;
    }

    showTotalCash() {

        let total = this.sumarizeAllAccounts();
        return `${(total / 10000).toFixed(2)} 万元`;
    }

    intoAmount() {
     
        let ref = this.typeds(this.states.current);
        if (this.isByRatio(ref) && this.hasAnyAccount()) {

            let pct = ref.limit.position.percentage;
            let pct_real = typeof pct != 'number' || pct <= 0 || pct >=100 ? 100 : pct;
            let total = this.sumarizeAllAccounts();
            let result = (total * pct_real * 0.01) / 10000;
            return result.toFixed(2) + '万元';
        }
        else {
            return '';
        }
    }

    /**
     * @param {BuyTask} task 
     */
    setAsTask(task) {
        this.aboutTask = task;
    }

    /**
     * @param {BuyTask} task 
     */
    handlePassiveSwitch(task) {

        this.log(`context task switched to: ${JSON.stringify(task)}`);
        this.setAsTask(task);
        const ref = this.states;
        const tinfo = ref.taskInfo;

        if (task) {

           tinfo.instrument = task.instrument;
           tinfo.instrumentName = task.instrumentName;
           tinfo.status = task.status;

            ref.choosed = task.strategyName;
            ref.current = this.helper.deepClone(task.strategy);
            setTimeout(() => { this.recalculateFigure(); }, 30);
        }
        else {

           tinfo.instrument = null;
           tinfo.instrumentName = null;
           tinfo.status = null;
           this.figures.clear();
        }

        /**
         * 已勾选 & 合约有效 & 非可融资合约，自动撤销已勾选
         */
        if (this.states.current.limit.creditFlag && tinfo.instrument && !this.isInstrumentCreditableBuy()) {
            this.states.current.limit.creditFlag = false;
        }

        this.handleInstrumentChange();
    }

    /**
     * @param {Array<BuyTask>} tasks 
     */
    tryUpdateTaskStatus(tasks) {

        const ref = this.aboutTask;

        if (tasks.length == 0 || !ref || this.helper.isNone(this.states.taskInfo.status)) {
            return;
        }

        let matched = tasks.find(x => x.id == ref.id || x.instrument == ref.instrument);
        if (matched) {
            this.states.taskInfo.status = matched.status;
        }
    }

    clearTask() {

        const ref = this.states.taskInfo;
        ref.instrument = null;
        ref.instrumentName = null;
        ref.status = null;
        this.handleInstrumentChange();
    }

    /**
     * @param {ZtStandardStrategy} data 
     */
    typeds(data) {
        return data;
    }

    /**
     * @param {Number} percentage 
     * @param {AccountSimple} account 
     * @param {Number} highest_price 
     */
    figureByAccount(percentage, account, highest_price) {
        
        const { max, decrease, topmost } = this.states.current.limit;
        const { accountId, accountName } = account;
        /** 总可用现金（元） */
        const total = this.summarizeSingle(account);
        /** 当前可用现金（元） */
        const canuse = total * percentage;
        /** 按涨停价预算当前可买数量（手），买入的手续费前后端均视为0.3%，计入价格因子再换算可买数量 */
        const total_hands = Math.floor(canuse / (highest_price * (1 + 0.3 / 100)) / 100);
        /** 单个委托最大证券数（手） */
        const max_turn_size = max.volume;
        /** 委托总规模（手） */
        const tscale = topmost.volume;
        /** 是否需要拆单 */
        const should_split = max.checked && max_turn_size >= 1 && max_turn_size <= total_hands;
        /** 是否需要限制下单总规模上限 */
        const should_limit_scale = topmost.checked && tscale >= 1 && tscale <= total_hands;

        // 无需拆单

        if(!should_split) {
            
            // 不拆单时，单个单子数量，仅和总规模上限比较
            let order_hands = should_limit_scale ? tscale : total_hands;
            return { accountId, accountName, volumes: [order_hands], total: order_hands };
        }

        // 根据最大委托数量限制单笔委托数量
        let turn_size = should_limit_scale ? Math.min(tscale, max_turn_size) : max_turn_size;
        let left = total_hands;
        let decrease_hands = decrease.checked ? 1 : 0;
        let volumes = [];

        while (left > 0) {

            let hands = turn_size - decrease_hands * volumes.length;
            if (hands <= 0) {
                break;
            }
            else if (left < hands) {
                volumes.push(left);
                break;
            }

            volumes.push(hands);
            left -= hands;
        }

        // 对拆单后形成的单子，检验加总数量，是否超过总规模上限

        if (should_limit_scale) {
            
            let real_hands = volumes.sum();
            if (real_hands > tscale) {

                let over_hands = real_hands - tscale;
                while (over_hands >= 1) {

                    let last_idx = volumes.length - 1;
                    let last_hands = volumes[last_idx];
                    
                    if (over_hands >= last_hands) {
                        
                        // 超限的数量，大于最后一个拆单数量
                        over_hands -= last_hands;
                        volumes.pop();
                    }
                    else {

                        // 最后一个拆单数量，足够
                        volumes[last_idx] = last_hands - over_hands;
                        over_hands = 0;
                        break;
                    }
                }
            }
        }

        return { accountId, accountName, volumes: volumes, total: volumes.sum() };
    }

    async recalculateFigure() {

        const instrument = this.states.taskInfo.instrument;
        if (!instrument) {
            
            this.figures.clear();
            return;
        }

        await this.requestAccounts();

        if (!this.hasAnyAccount()) {

            this.figures.clear();
            return;
        }

        var pinfo = await this.requestPriceInfo(instrument);
        if (pinfo.upperLimitPrice == 0) {

            this.figures.clear();
            return;
        }

        const { position } = this.states.current.limit;
        let total = this.sumarizeAllAccounts();
        let allocated = this.isByMoney(this.states.current) ? Math.min(total, (position.amount || ********) * 10000) : (total * (position.percentage || 100) / 100);
        let percentage = total == 0 ? 0 : allocated / total;
        let available_accounts = this.filterAccountsByMarket();
        let results = available_accounts.map(acnt => this.figureByAccount(percentage, acnt, pinfo.upperLimitPrice || 1));

        // console.log({

        //     limit: this.helper.deepClone(this.states.current.limit),
        //     total,
        //     allocated,
        //     percentage,
        //     available_accounts: this.helper.deepClone(available_accounts),
        //     pinfo,
        //     results: this.helper.deepClone(results),
        // });

        this.figures.refill(results);
    }

    /**
     * 汇总所有账号的总可用（可用资金 + 可用融资余额）
     * @returns {Number}
     */
    sumarizeAllAccounts() {

        let available_accounts = this.filterAccountsByMarket();
        return available_accounts.map(x => this.summarizeSingle(x)).sum();
    }

    /**
     * 汇总单个账号的总可用（可用资金 + 可用融资余额）
     * @param {AccountSimple} account
     */
    summarizeSingle(account) {
        
        let { available, enableCreditBuy } = account;
        /** 总可用现金 = 可用本金 + 融资可用 */
        let total = available;
        if (this.states.current.limit.creditFlag && account.credit && typeof enableCreditBuy == 'number') {
            total += Math.max(0, enableCreditBuy);
        }

        /**
         * 前后端协同将现金下折
         * 在原始总可用基础上进行下折，而非计算了对应比例现金后，再进行下折
         */
        return total * 0.99;
    }

    isInstrumentCreditableBuy() {

        let instrument = this.states.taskInfo.instrument;
        if (!instrument) {
            return false;
        }

        let matches = BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, instrument, true);
        return !!matches.find(x => x.creditBuy == 1);
    }

    /**
     * @param {CriteriaParam} cp 
     */
    isChoiceMode(cp) {
        return Array.isArray(cp.choices) && cp.choices.length > 0;
    }

    handleInstrumentChange() {

        let trigger = this.states.current.btriggers.find(x => x.variable == 'customPriceOpen');
        if (trigger) {

            let cp = trigger.conditions.find(x => x.variable == 'customPriceType');
            if (cp) {
                this.handleInputCdtChange(cp, trigger);
            }
        }
    }

    /**
     * @param {CriteriaParam} cp 
     * @param {BuyCancelTrigger} trigger 
     */
    async handleInputCdtChange(cp, trigger) {

        let handler = TriggerHandlers[cp.handler];

        if (typeof handler == 'function') {
            try {
                await handler(cp, trigger, this.states.taskInfo.instrument);
            }
            catch (ex) {
                console.error(ex);
            }
        }

        this.handleParamChange();
    }

    hasAnyCreditAccount() {
        return this.accounts.some(x => !!x.credit);
    }

    hasAnyAccount() {
        return this.accounts.length > 0;
    }

    async requestAccounts() {

        var resp = await repoAccount.getAccountDetailInfo({ userId: this.userInfo.userId });
        var { errorCode, errorMsg, data } = resp;
        var records = (data || {}).list || [];
        var accounts = errorCode == 0 ? AccountSimple.Convert(records) : [];
        this.accounts.refill(accounts);
    }

    async requestPriceInfo(instrument) {

        var output = { instrument, preClosePrice: 0, lastPrice: 0, upperLimitPrice: 0, lowerLimitPrice: 0 };
        if (!instrument) {
            return output;
        }

        var resp = await repoInstrument.queryPrice(instrument);
        var { errorCode, errorMsg, data } = resp;
        var { assetType, preClosePrice, lastPrice, lowerLimitPrice, optionType, priceTick, strikePrice, upperLimitPrice, volumeMultiple } = data || {};

        if (errorCode == 0 && data && upperLimitPrice > 0) {

            output.preClosePrice = preClosePrice;
            output.lastPrice = lastPrice;
            output.upperLimitPrice = upperLimitPrice;
            output.lowerLimitPrice = lowerLimitPrice;
        }

        return output;
    }

    /**
     * @param {CriteriaParam} cp 
     */
    isConditionOk(cp) {
        
        let minv = typeof cp.min == 'number' ? cp.min : -********9999;
        let maxv = typeof cp.max == 'number' ? cp.max : ********9999;
        return cp.threshold >= minv && cp.threshold <= maxv;
    }

    /**
     * @param {Array<CriteriaParam>} cps 
     */
    makeConditionNotice(cps) {
        
        let msgs = [];
        cps.forEach(cdt => {
            let has_min = typeof cdt.min == 'number';
            let has_max = typeof cdt.max == 'number';
            if (has_min && has_max) {
                msgs.push(`${cdt.min} <= ${cdt.unit} <= ${cdt.max}`);
            }
            else if (has_max) {
                msgs.push(`${cdt.unit} <= ${cdt.min}`);
            }
            else if (has_min) {
                msgs.push(`${cdt.unit} >= ${cdt.min}`);
            }
            else {
                msgs.push(`${cdt.unit} >= 1`);
            }
        });

        return msgs.join(' / ');
    }

    /**
     * @param {ZtStandardStrategy} setting
     * @param {ZtStandardStrategy} current
     */
    validate(setting, current) {

        // 检测策略设置时，如果对应为当前选中策略，直接检测当前选中的策略最新输入版本
        let which = setting.name == current.name ? current : setting;
        let { btriggers, ctriggers } = which;
        let result = { isOk: true, message: null, setting };

        let err_buy = btriggers.find(x => {

            let oks = x.conditions.filter(y => this.isConditionOk(y));
            let partial_ok = oks.length < x.conditions.length;
            return x.checked && partial_ok;
        });

        if (err_buy) {

            result.isOk = false;
            result.message = `买入触发条件 > ${err_buy.name}，需满足：${this.makeConditionNotice(err_buy.conditions)}`;
        }

        let err_cancel = ctriggers.find(x => {

            let oks = x.conditions.filter(y => this.isConditionOk(y));
            let partial_ok = oks.length < x.conditions.length;
            return x.checked && partial_ok;
        });

        if (err_cancel) {
            
            result.isOk = false;
            result.message = `撤单条件 > ${err_cancel.name}，需满足: ${this.makeConditionNotice(err_cancel.conditions)}`;
        }

        if (result.isOk) {

            /**
             * 买卖策略参数均验证通过时，再对规模限制参数进行检查
             */

            let { isOk, error } = this.checkLimit(which);
            result.isOk = isOk;
            result.message = error;
        }

        return result;
    }

    startTask() {

        this.log(`to [start] the task by strategy panel button, task info/${JSON.stringify(this.states.taskInfo)}`);
        this.trigger('start-task');
    }

    stopTask() {

        this.log(`to [stop] the task by strategy panel button, task info/${JSON.stringify(this.states.taskInfo)}`);
        this.trigger('stop-task');
    }

    cancelTask() {

        this.log(`to [cancel] the task by strategy panel button, task info/${JSON.stringify(this.states.taskInfo)}`);
        this.trigger('cancel-task');
    }

    reset() {

        var ref = this.states;
        var matched = this.settings.find(x => x.name == ref.choosed);

        if (!matched) {
            return;
        }

        ref.current = this.helper.deepClone(matched);
        this.interaction.showSuccess('已重置参数');
        this.log(`press button to reset the focused strategy: ${JSON.stringify(ref.current)}`);
        this.handleParamChange();
    }

    /**
     * @param {String} message 
     */
    log(message) {
        this.loggerTrading.info('ztlog:' + message);
    }

    /**
     * @param {String} message 
     */
    logFatal(message) {
        this.loggerTrading.fatal('ztlog:' + message);
    }

    canStart() {
        return TaskObject.isTaskCreated(this.states.taskInfo.status) || TaskObject.isTaskPaused(this.states.taskInfo.status);
    }

    canStop() {
        return TaskObject.isTaskRunning(this.states.taskInfo.status);
    }

    canCancel() {
        return TaskObject.isTaskOrdered(this.states.taskInfo.status) && this.isTaskNotCanceled();
    }

    isTaskNotCanceled() {
        return this.aboutTask && !this.aboutTask.hasCanceled;
    }

    build($container) {

        super.build($container);
        this.createToolbar();
        this.requestSettings();
        this.requestAccounts();
    }
};