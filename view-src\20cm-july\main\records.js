const { BaseView } = require('../base-view');
const { SmartTable } = require('../../../libs/table/smart-table');
const { Order } = require('../../../model/order');
const { Position } = require('../../../model/position');
const { ModelConverter } = require('../../../model/model-converter');
const { repoOrder } = require('../../../repository/order');
const { repoPosition } = require('../../../repository/position');
const { ZtSetting } = require('../objects');
const MouseTrap = require('mousetrap');
const { AccountSummaryInfo } = require('../model/objects');

/**
 * @returns {Array<Position>}
 */
function MakePositions() {
    return [];
}

class AggregatedPosition {

    /**
     * @param {Position} struc 
     */
    constructor(struc, weightedPrice) {

        this.instrument = struc.instrument;
        this.instrumentName = struc.instrumentName;
        this.pricePrecision = struc.pricePrecision;
        this.weightedPrice = weightedPrice;

        this.totalPosition = struc.totalPosition;
        this.closableVolume = struc.closableVolume;
        this.marketValue = struc.marketValue;
        this.yesterdayPosition = struc.yesterdayPosition;
        this.frozenVolume = struc.frozenVolume;
        this.profit = struc.profit;
        this.profitPct = 0;
        this.lastPrice = 0;
        // 账号总资产
        this.accountTotalAsset = 0;
    }
}

/**
 * 一个新的订单，首次发生成交（买入方向）时，需要进行提示（仅提示1次），该map存储数据为：订单id/true，标识为已提示
 */
const GNotifyingMap = {};

class RecordsView extends BaseView {

    get notifyingMap() {
        return GNotifyingMap;
    }

    constructor() {

        super('@20cm-july/main/records', false, '交易数据');
        this.tabs = { entrust: 1, positon: 2 };
        this.positions = MakePositions();
        this.ztsetting = ZtSetting.makeDefault();
        this.summarySh = AccountSummaryInfo.CreateEmptySummary();
        this.summarySz = AccountSummaryInfo.CreateEmptySummary();
    }
    
    /**
     * @param {AccountSummaryInfo} summarySh 
     * @param {AccountSummaryInfo} summarySz 
     */
    attachAccounts(summarySh, summarySz) {

        Object.assign(this.summarySh, summarySh);
        Object.assign(this.summarySz, summarySz);
        const positions = this.typedAsPositions(this.tablePosition.extractAllRecords());
        positions.forEach(pos => {
            this.tablePosition.updateRow({
                instrument: pos.instrument,
                accountTotalAsset: pos.accountTotalAsset,
            });
        });
    }

    /**
     * @param {ZtSetting} setting 
     */
    setAsSetting(setting) {
        Object.assign(this.ztsetting, this.helper.deepClone(setting));
        // const ref = MouseTrap;
        // if (this.cachedCancelStroke) {
        //   ref.unbind(this.cachedCancelStroke, 'keyup');
        // }
        // if (this.ztsetting.cancelStroke) {
        //   this.cachedCancelStroke = this.ztsetting.cancelStroke.toLowerCase();
        //   ref.bind(this.cachedCancelStroke, this.shortcutCancel.bind(this), 'keyup');

        // }
    }

    // shortcutCancel() {
    //   if (this.isEntrustFocused()) {
    //     var selectedOrder = this.tableObj.selectedRow;
    //     if (selectedOrder) {
    //       this.cancel(selectedOrder);
    //     }
    //   }
    // }

    createToolbar() {

        this.states = {
            
            focused: this.tabs.entrust,
            onlyCancellable: true,
            isIntegrated: false,
            isOccupied: false,
            keywords: {
                order: null,
                position: null,
            }
        };

        const vapp = new Vue({

            el: this.$container.querySelector('.view-toolbar'),
            data: {

                states: this.states,
                tabs: this.tabs,
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.handleTabChange,
                this.handleFilterChange,
                this.cancelChecks,
                this.cancelAll,
                this.popout,
                this.occupy,
                this.refreshOrders,
                this.refreshPositions,
                this.filterPositions,
            ]),
        });

        vapp.$nextTick(() => { this.handleTabChange(); });
    }

    isEntrustFocused() {
        return this.states.focused == this.tabs.entrust;
    }

    handleTabChange() {

        const t1 = this.tableOrder;
        const t2 = this.tablePosition;

        if (this.isEntrustFocused()) {

            t2.$component.style.display = 'none';
            t1.$component.style.display = 'block';
            t1.fitColumnWidth();
        }
        else {

            t1.$component.style.display = 'none';
            t2.$component.style.display = 'block';
            t2.fitColumnWidth();
        }
    }

    popout() {
        this.trigger('popout');
    }

    handleFilterChange() {
        this.tableOrder.customFilter((record) => { return this.testOrder(record); });
    }

    cancelChecks() {

        const checkes = this.tableOrder.extractCheckedScreenRecords();
        const orders = this.typedAsOrders(checkes);

        if (orders.length == 0) {

            this.interaction.showError('请勾选委托单');
            return;
        }

        const cancellables = orders.filter(x => !x.isCompleted);
        if (cancellables.length == 0) {

            this.interaction.showError('勾选委托单，没有可撤单');
            return;
        }

        this.confirm(true, `是否撤销勾选，可撤数量 = ${cancellables.length} ?`, () => {
            
            const abbrs = cancellables.map(x => ({ id: x.id, instrument: x.instrument, instrumentName: x.instrumentName }));
            this.log(`to cancel checked all cancellable orders: ${JSON.stringify(abbrs)}`);

            cancellables.forEach(x => {
                this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.cancelOrder, { orderId: x.id });
            });
            
            this.tableOrder.uncheckAll();
            this.interaction.showSuccess(`批量撤单请求已发出`);
        });
    }

    cancelAll() {
        
        const list = this.tableOrder.extractAllRecords();
        const orders = this.typedAsOrders(list);

        if (orders.length == 0) {

            this.interaction.showError('当前没有委托单');
            return;
        }
        
        const cancellables = orders.filter(x => !x.isCompleted);
        if (cancellables.length == 0) {

            this.interaction.showError('没有可撤销的委托单');
            return;
        }

        this.confirm(true, `是否撤销全部委托，数量 = ${cancellables.length} ?`, () => {
            
            const abbrs = cancellables.map(x => ({ id: x.id, instrument: x.instrument, instrumentName: x.instrumentName }));
            this.log(`to cancel all orders: ${JSON.stringify(abbrs)}`);

            cancellables.forEach(x => {
                this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.cancelOrder, { orderId: x.id });
            });
            
            this.tableOrder.uncheckAll();
            this.interaction.showSuccess(`批量撤单请求已发出`);
        });
    }

    /**
     * @param {Order} record
     */
    testOrder(record) {

        let is_status_ok = !this.states.onlyCancellable || this.states.onlyCancellable && !record.isCompleted;
        let keywords = this.states.keywords.order;
        let is_kw_ok = !keywords
                    || record.instrument.indexOf(keywords) >= 0
                    || record.instrumentName.indexOf(keywords) >= 0 
                    || this.helper.pinyin(record.instrumentName).indexOf(keywords) >= 0;

        return is_status_ok && is_kw_ok;
    }

    filterPositions() {
        this.tablePosition.customFilter((record) => { return this.testPosition(record); });
    }
    
    /**
     * @param {AggregatedPosition} record
     */
    testPosition(record) {

        let keywords = this.states.keywords.position;
        let is_ok = !keywords
                    || record.instrument.indexOf(keywords) >= 0
                    || record.instrumentName.indexOf(keywords) >= 0 
                    || this.helper.pinyin(record.instrumentName).indexOf(keywords) >= 0;

        return is_ok;
    }


    occupy() {
        this.trigger('occupy-change', (this.states.isOccupied = !this.states.isOccupied));
    }

    /**
     * @param {Order} record 
     */
    identifyOrder(record) {
        return record.id;
    }

    /**
     * @returns {Array<Order}
     */
    typedAsOrders(records) {
        return records;
    }

    createTableOrder() {
        
        const $table = this.$container.querySelector('.table-order');
        const table = new SmartTable($table, this.identifyOrder, this, {

            tableName: 'smt-20cm-july-order',
            displayName: '委托',
            defaultSorting: { prop: 'orderTime', direction: 'desc' },
            // virtual: true,
            rowDbClicked: this.handleOrderRowPunch.bind(this),
        });

        table.setPageSize(99999);
        this.tableOrder = table;
    }

    /**
     * @returns {Array<AggregatedPosition}
     */
    typedAsPositions(records) {
        return records;
    }

    /**
     * @param {AggregatedPosition} record 
     */
    identifyPosition(record) {
        return record.instrument;
    }

    /**
     * @param {Order} record 
     */
    handleOrderRowPunch(record) {

        if (!this.ztsetting.doubleClick2Cancel) {
            return;
        }

        this.log(`user double clicked an entrust to cancel: ${JSON.stringify(record)}`);
        this.cancel(record);
    }

    /**
     * @param {AggregatedPosition} record 
     */
    handlePositionRowPunch(record) {

        var { instrumentName, closableVolume } = record;
        
        if (closableVolume <= 0) {
            this.interaction.showError(`${instrumentName}，无可平仓位`);
        }

        this.log(`double click a position record, ${JSON.stringify(record)}`);
        this.trigger('hit-position', record);
    }

    /**
     * @returns {AggregatedPosition}
     */
    findPosition(instrument) {

        /**
         * 外界所关心的某个合约的持仓变动
         */
        this.caringInstrument = instrument;
        return this.tablePosition.getRowData(instrument);
    }

    createTablePosition() {
        
        const $table = this.$container.querySelector('.table-position');
        const table = new SmartTable($table, this.identifyPosition, this, {

            tableName: 'smt-20cm-july-position',
            displayName: '持仓',
            defaultSorting: { prop: 'createTime', direction: 'desc' },
            // virtual: true,
            rowDbClicked: this.handlePositionRowPunch.bind(this),
        });

        table.setPageSize(99999);
        this.tablePosition = table;
    }

    /**
     * 单一撤单
     * @param {Order} order 
     */
    cancel(order) {

        this.confirm(false, `是否撤销：${order.instrumentName} ?`, () => {

            this.log(`to cancel an order: ${JSON.stringify(order)}`);
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.cancelOrder, { orderId: order.id });
            this.interaction.showSuccess(`${order.instrumentName}，撤单请求已发出`);
        });
    }

    /**
     * @param {Order} record
     */
    formatActions(record) {
        return record.isCompleted ? '' : '<button class="danger" event.onclick="cancel">撤单</button>';
    }

    async requestOrders() {

        var resp = await repoOrder.batchMemQuery({ trade_user_id: this.userInfo.userId });
        if (resp.errorCode != 0) {

            this.interaction.showError(`委托查询错误：${resp.errorCode}/${resp.errorMsg}`);
            return;
        }
        
        var records = resp.data;
        /** 首行数据，为标题栏 */
        var titles = records.shift();
        var orders = ModelConverter.formalizeOrders(titles, records);

        /**
         * 委托时间，后端返回格式不规整，需在前面补0，进行对齐（便于排序功能的一致性）
         */

        orders.forEach(item => {

            let ot = item.orderTime;
            if (typeof ot == 'string' && ot.length == 5 || typeof ot == 'number' && ot < 100000) {
                item.orderTime = '0' + ot;
            }
        });
        
        this.tableOrder.refill(orders);
        this.mapNotifieds(orders);
        this.handleFilterChange();
    }

    /**
     * @param {Array<Order>} orders
     */
    mapNotifieds(orders) {

        var stses = this.systemEnum.orderStatus;
        orders.forEach(order => {

            let tradedAll = order.orderStatus == stses.traded.code;
            let tradedPartial = order.orderStatus == stses.partialTraded.code;

            if (order.parentOrderId && (tradedAll || tradedPartial)) {
                this.notifyingMap[order.id] = true;
            }
        });
    }

    /**
     * @param {Order} order
     */
    ask2Notify(order) {

        var stses = this.systemEnum.orderStatus;
        var dirs = this.systemTrdEnum.tradingDirection;
        var tradedAll = order.orderStatus == stses.traded.code;
        var tradedPartial = order.orderStatus == stses.partialTraded.code;
        var isBuy = order.direction == dirs.buy.code;
        var pid = order.parentOrderId;
        var isRequired = pid && (tradedAll || tradedPartial) && this.notifyingMap[pid] === undefined;

        if (!isRequired) {
            return;
        }

        this.notifyingMap[pid] = true;
        let directionLabel = isBuy ? '买入' : '卖出';
        this.interaction.notify({

            title: `${directionLabel}提示`,
            type: 'success',
            position: 'bottom-right',
            message: `${order.instrument}/${order.instrumentName}，已${isBuy ? '买入' : '卖出'}${order.tradedVolume}`,
        });

        this.ring4OrderFirstTrade(isBuy);
    }

    /**
     * @param {*} struc
     */
    handleOrderChange(struc) {

        var order = new Order(struc);
        var is4Me = order.userId == this.userInfo.userId;
        var isAdjustPos = order.adjustFlag;

        if (!is4Me || isAdjustPos) {
            return;
        }

        this.log(`received an order change notify: ${JSON.stringify(struc)}`);
        this.tableOrder.putRow(order);
        this.ask2Notify(order);

        if (this.states.onlyCancellable && order.isCompleted) {
            this.handleFilterChange();
        }
    }

    listen2Change() {
        this.standardListen(this.serverEvent.orderChanged, this.handleOrderChange.bind(this));
    }

    subChange() {
        this.standardSend(this.systemEvent.subscribeAccountChange, null, [this.serverEvent.orderChanged]);
    }

    unsubChange() {
        this.standardSend(this.systemEvent.unsubscribeAccountChange, null, [this.serverEvent.orderChanged]);
    }

    dispose() {
        this.unsubChange();
    }

    refresh() {
        
        this.interaction.showSuccess('刷新请求已发出');
        this.isEntrustFocused() ? this.requestOrders() : this.requestPositions();
    }

    refreshOrders() {

        this.interaction.showSuccess('委托：刷新请求已发出');
        this.requestOrders();
    }

    refreshPositions() {

        this.interaction.showSuccess('持仓：刷新请求已发出');
        this.requestPositions();
    }

    async requestPositions() {

        var resp = await repoPosition.batchMemQuery({ trade_user_id: this.userInfo.userId });
        if (resp.errorCode != 0) {

            this.interaction.showError(`持仓查询错误：${resp.errorCode}/${resp.errorMsg}`);
            return;
        }

        var records = resp.data;
        /** 首行数据，为标题栏 */
        var titles = records.shift();
        var positions = ModelConverter.formalizePositions(titles, records);
        this.positions.refill(positions);
        
        var aggregateds = [new AggregatedPosition({}, 0)].splice(1);
        var map = {};

        positions.forEach(item => {
            
            let matched = map[item.instrument];
            if (matched === undefined) {

                let sames = positions.filter(x => x.instrument == item.instrument);
                let tpos = sames.map(x => x.totalPosition).sum();
                let wprice = tpos == 0 ? 0 : sames.map(x => x.totalPosition * x.avgPrice / tpos).sum();
                let agg = new AggregatedPosition(item, wprice);
                aggregateds.push(agg);
                map[item.instrument] = agg;
            }
            else if (matched instanceof AggregatedPosition) {

                matched.totalPosition += item.totalPosition;
                matched.closableVolume += item.closableVolume;
                matched.marketValue += item.marketValue;
                matched.yesterdayPosition += item.yesterdayPosition;
                matched.frozenVolume += item.frozenVolume;
                matched.profit += item.profit;
            }
        });
        
        // 合并两市总资产
        const totalAsset = (this.summarySh.balance || 0) + (this.summarySz.balance || 0);
        // 合并两市昨日总资产
        const pretotalAsset = (this.summarySh.preBalance || 0) + (this.summarySz.preBalance || 0);

        /**
         * 计算盈亏比例
         */
        aggregateds.forEach(item => {

            // 关联所在账号总资产
            item.accountTotalAsset = totalAsset;
            // 单票盈亏金额 / 账号总资产
            item.profitPct = totalAsset != 0 ? item.profit / totalAsset : null;
        });

        this.tablePosition.refill(aggregateds);

        /**
         * 主动式上报当前外界所关心的持仓
         */
        let caring = aggregateds.find(x => x.instrument == this.caringInstrument);
        if (caring) {
            this.trigger('cared-position-update', caring);
        }
        
        const totalPosProfit = aggregateds.reduce((sum, item) => sum + item.profit, 0);
        const details = aggregateds.map(item => {
            const { instrument, instrumentName, profit } = item;
            return { instrument, instrumentName, profit };
        });

        this.trigger('position-update', { totalAsset, pretotalAsset, totalPosProfit, details });
    }

    arrangePositionUpdate() {

        setInterval(async () => {
            
            if (this.isBackgroundRefresh) {
                return;
            }

            try {

                this.isBackgroundRefresh = true;
                await this.requestPositions();
            }
            catch(ex) {
                console.error(ex);
            }
            finally {
                this.isBackgroundRefresh = false;
            }

        }, 1000 * 5);
    }

    exportSome() {

        var date = new Date().format('yyyyMMdd');
        if (this.isEntrustFocused()) {
            this.tableOrder.exportAllRecords(`委托-${this.userInfo.userName}-${date}`);
        }
        else {
            this.tablePosition.exportAllRecords(`持仓-${this.userInfo.userName}-${date}`);
        }
    }

    autoFit() {

        this.registerEvent('auto-fit', () => { 
            this.tableOrder.fitColumnWidth(); 
            this.tablePosition.fitColumnWidth(); 
        });
    }

    build($container) {

        super.build($container);
        this.createToolbar();
        this.createTableOrder();
        this.createTablePosition();
        this.autoFit();
        this.listen2Change();
        this.subChange();
        this.requestOrders();
        this.requestPositions();
        this.arrangePositionUpdate();
        this.renderProcess.on(this.systemEvent.tradingServerReestablished, () => { this.subChange(); });
    }
};


module.exports = {

    AggregatedPosition,
    RecordsView,
};