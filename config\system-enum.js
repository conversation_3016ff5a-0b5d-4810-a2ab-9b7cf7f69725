﻿/**
 * general enumerations
 */

const systemEnum = {

    instructionTypes: {

        normal: { code: 0, mean: '普通交易' },
        algorithm: { code: 1, mean: '算法交易' },
    },

    examineStatuses: {

        passed: { code: 0, mean: '通过' },
        waiting: { code: 1, mean: '待审批' },
        rejected: { code: 2, mean: '驳回' },
    },

    assetsType: {

        future: { code: 1, mean: '期货' },
        stock: { code: 2, mean: '股票' },
        option: { code: 3, mean: '期权' },
        bond: { code: 4, mean: '债券' },
        fund: { code: 5, mean: '公募基金' },
        buyback: { code: 7, mean: '回购' },
    },

    assetsTypes: {

        future: { code: 1, mean: '期货' },
        stock: { code: 2, mean: '股票' },
        option: { code: 3, mean: '期权' },
        bond: { code: 4, mean: '债券' },
        fund: { code: 5, mean: '公募基金' },
        buyback: { code: 7, mean: '回购' },
    },

    identityType: {

        account: { code: 1, mean: '账号' },
        strategy: { code: 2, mean: '策略' },
        fund: { code: 3, mean: '产品' },
    },

    accountStatus: {

        started: { code: 0, mean: '新创建' },
        disabled: { code: 1, mean: '已下线' },
        enabled: { code: 2, mean: '已上线' },
        tradingEnabled: { code: 4, mean: '允许交易' },
        tradingDisabled: { code: 3, mean: '禁止交易' },
        tradingPaused: { code: 5, mean: '交易暂停' },
        notInitialized: { code: 6, mean: '账号尚未初始化' },
        discarded: { code: 7, mean: '账号已废弃' },
    },

    terminalInterface: {

        stock: { code: 1, matchedAssetType: 2, mean: '股票终端', flagBgColor: 's-bg-red' },
        future: { code: 2, matchedAssetType: 1, mean: '期货终端', flagBgColor: 's-bg-green' },
        stockOption: { code: 3, matchedAssetType: 3, mean: '股票期权终端', flagBgColor: 's-bg-orange' },
    },

    orderStatus: {

        created: { code: 10, mean: '新建' },
        notConfirmed: { code: 13, mean: '待确认' },
        confirmed: { code: 15, mean: '已报' },
        notTraded: { code: 16, mean: '未成' },
        partialTraded: { code: 17, mean: '部成' },
        partialCanceled: { code: 18, mean: '部撤', isCompleted: true },
        traded: { code: 19, mean: '全成', isCompleted: true },
        invalid: { code: 30, mean: '废单', isCompleted: true },
        toCancel: { code: 53, mean: '待撤' },
        canceled: { code: 54, mean: '已撤单', isCompleted: true },
        tobeAudit: { code: 0, mean: '待审核' },
        reject: { code: 55, mean: '已驳回', isCompleted: true },
    },

    poolTradingType: {

        concept: { code: 1, mean: '概念' },
        industry: { code: 2, mean: '行业' },
        self: { code: 3, mean: '自定义组合' },
    },

    recordEventType: {

        order: { code: 2, mean: '下单操作' },
        keyboard: { code: 3, mean: '键盘操作' },
        mouse: { code: 4, mean: '鼠标操作' },
        configuration: { code: 5, mean: '配置操作' },
    },

    /**
     * 下单代码来源
     */
    orderInstrumentSource: {

        manual: { mean: '人工输入', code: 1 },
        signal: { mean: '信号关联', code: 2 },
        tradableStock: { mean: '券池关联', code: 3 },
        self: { mean: '自定义组合', code: 4 },
        push: { mean: '推送组合', code: 5 },
        industry: { mean: '行业组合', code: 6 },
        concept: { mean: '概念组合', code: 7 },
        feature: { mean: '特征组合', code: 8 },
    },

    userType: {

        xtrade: { code: 10, mean: 'PB用户' },
        dayAdmin: { code: 20, mean: '日内管理员' },
        dayRisk: { code: 21, mean: '日内风控员' },
        dayTrader: { code: 22, mean: '日内交易员' },
    },

    yesNo: {

        yes: { code: true, mean: '是' },
        yes2: { code: 1, mean: '是' },
        no: { code: false, mean: '否' },
        no2: { code: 0, mean: '否' },
    },
};

module.exports = { systemEnum };
