const { helper } = require('../../libs/helper');

class LabelValue {

    constructor(label, value) {

        this.label = label;
        this.value = value;
    }
}

class Definition {

    constructor(code, mean) {

        this.code = code;
        this.mean = mean;
    }
}

/**
 * @returns {Array<{ label: string, value: number | string | boolean }>}
 */
function typeChoices(choices) {
    return choices;
}

/**
 * 买卖策略配置项，单项内组成条件（多个）
 * 对应为用户授权文件内，conditions 项下的配置项结构
 */
class CriteriaParam {

    constructor({

        /** [required] [string] 变量名称 */
        variable, 
        /** [optional] [string] 比较方式 */
        compare, 
        /** [optional] [number] 实际输入值，进行比对的阈值 */
        threshold,
        /** [optional] [Array<{ label: string, value: number | string | boolean }>] 可用选项（硬编码静态配置），如果提供该字段，则输入组件渲染为下拉框 */
        choices,
        /** [optional] [string] 输入项数值，单位（显示用途） */
        unit, 
        /** [optional] [number] 输入项数值，最大值 */
        max, 
        /** [optional] [number] 输入项数值，最小值 */
        min,
        /** [optional] [number] 输入项数值，最终输出，需要乘以的倍数 */
        times,
        /** [optional] [string] 输入项数值变化处理函数（强制异步） */
        handler,
        /** [optional] [boolean] 是否只读 */
        readonly,
        /** [optional] [boolean] 是否可见 */
        visible,
    }) {

        /** [required] [string] 参数名称 */
        this.variable = variable;
        /** [optional] [string] 和阈值进行比较，的比较符号 */
        this.compare = compare;
        /** [optional] [number] 阈值 */
        this.threshold = threshold;
        /** [optional] [number] 阈值 */
        this.choices = typeChoices(choices);
        /** [optional] [string] 展示单位 */
        this.unit = unit;
        /** [optional] [number] 阈值最大可到多少（包含） */
        this.max = max;
        /** [optional] [number] 阈值最小可到多少（包含） */
        this.min = min;
        /** [optional] [number] 乘数，提交到服务器 × times，服务器推送 / times，默认为1 */
        this.times = typeof times == 'number' && times != 0 ? times : 1;
        /** [optional] [string] 输入项数值变化处理函数（强制异步） */
        this.handler = handler;
        /** [optional] [boolean] 是否只读 */
        this.readonly = readonly === true || readonly === 1;
        /** [optional] [boolean] 是否可见 */
        this.visible = visible !== false && visible !== 0;
    }
}

/**
 * 买入、撤单、触发先决条件
 */
class BuyCancelTrigger {

    constructor(struc) {
        
        let { name, variable, checked, conditions } = struc || {};
        this.name = name;
        this.variable = variable;
        this.checked = !!checked;
        /** 组成该触发条件的，多个买入参数 */
        this.conditions = conditions instanceof Array ? conditions.map(x => new CriteriaParam(x)) : [];
    }
}

class ZtStandardStrategy {

    constructor (name, is_temp) {

        this.name = name;
        this.isTemp = is_temp === true;
        /** 买入触发条件 */
        this.btriggers = [new BuyCancelTrigger({})].splice(1);
        /** 撤单触发条件 */
        this.ctriggers = [new BuyCancelTrigger({})].splice(1);
        /** 交易规模限制 */
        this.limit = {
            max: { checked: false, volume: 100 },
            topmost: { checked: false, volume: 0 },
            decrease: { checked: false },
            position: { method: 5, percentage: 0, amount: 0 },
            creditFlag: false,
        };
    }
}

class ShortcutConfig {

    /**
     * @param {String} stroke 
     * @param {*} strategy 
     * @param {*} data 
     */
    constructor(stroke, strategy, data) {

        this.stroke = stroke;
        this.strategy = strategy;
        this.data = data || {};
    }

    static CreateNew() {
        return new ShortcutConfig(null, null);
    }
}

class ZtSetting {

    constructor({ 
        doubleClick2Cancel, 
        biggerFont, 
        manualConfirm, 
        strategy, 
        strategy2, 
        strategy3, 
        strategy4, 
        strategy5, 
        immediate, 
        cancelStroke, 
        buy,
    }) {

        /** 双击撤单 */
        this.doubleClick2Cancel = !!doubleClick2Cancel;
        /** 大号字体 */
        this.biggerFont = !!biggerFont;        
        /** 同花顺 */
        this.ths = {

            /** 是否需要人工确认弹窗 */
            manualConfirm: !!manualConfirm,
            // 所有默认快捷下单策略
            strategy: strategy || null,
            strategy2: strategy2 || null,
            strategy3: strategy3 || null,
            strategy4: strategy4 || null,
            strategy5: strategy5 || null,
            /** 立即启动 */
            immediate: !!immediate,
        };

        /** 撤单快捷键 */
        this.cancelStroke = cancelStroke;
        /** 手动买入快捷键设置 */
        this.buy = Object.assign({

            manualConfirm: true,
            stroke: null,
            type: null,
            amount: 1,
            ratio: 1,
            level: 0,
        }, buy);
    }

    static makeDefault() {

        return new ZtSetting({ 

            doubleClick2Cancel: false,
            biggerFont: false,
            manualConfirm: false,
            strategy: null,
            strategy2: null,
            strategy3: null,
            strategy4: null,
            strategy5: null,
            immediate: false,
            cancelStroke: 'F1', 

            buy: {
                
                manualConfirm: true,
                stroke: 'F2',
                type: 'amount',
                amount: 1,
                ratio: 1,
                level: 0
            }
        });
    }
}

class ZtRington {

    constructor(code, mean, mediaUrl) {
        
        this.code = code;
        this.mean = mean;
        this.mediaUrl = mediaUrl;
        this.isCustomized = !mediaUrl;
    }
}

const TaskStatus = {

    created: { label: '新建', value: 0 },
    started: { label: '运行中', color: 'green', value: 1 },
    ordered: { label: '已下单', color: 'orange', value: 2 },
    supplemented: { label: '补单中', color: 'purple', value: 3 },
    stopped: { label: '已停止', color: 'red', value: 4 },
    deleted: { label: '已删除', color: '#EE82EE', value: 5 },
    finished: { label: '已完成', color: '#FFE4E1', value: 6 },
};

const TaskStatuses = [

    TaskStatus.created, 
    TaskStatus.started,
    TaskStatus.ordered,
    TaskStatus.supplemented,
    TaskStatus.stopped,
    TaskStatus.deleted,
    TaskStatus.finished,
];

const Digits = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'];
const FunctionKeys = ['F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12'];
const KeyStrokes4Buy = Digits.concat(FunctionKeys);
const KeyStrokes4Sell = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'];

const Entrance = {

    makeFollowPrices() {

        var levels = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
        return levels.map(x => new Definition(x, `买${x}价`));
    },

    makeRings() {

        var path = require('path');
        return [

            new ZtRington(1, '提示音1', path.join(__dirname, '../../asset/rington/01.wav')),
            new ZtRington(2, '提示音2', path.join(__dirname, '../../asset/rington/02.wav')),
            new ZtRington(3, '提示音3', path.join(__dirname, '../../asset/rington/03.wav')),
            new ZtRington(4, '提示音4', path.join(__dirname, '../../asset/rington/04.wav')),
            new ZtRington(99, '自定义'),
        ];
    },

    makeDefaultRingtone() {

        const ringtons = this.makeRings();
        
        return {

            entrusted: ringtons[0].code,
            bought: ringtons[1].code,
            canceled: ringtons[2].code,
            sold: ringtons[3].code,
            
            customized: {

                entrusted: null,
                bought: null,
                canceled: null,
                sold: null,
            },
        };
    },
};

class BuyTask {

    constructor(struc = {

        localId, 
        id, 
        instrument, 
        instrumentName,
        direction,
        strategyId, 
        strategyName, 
        strategy, 
        percent, 
        ceilingPrice, 
        status, 
        hasCanceled,
        targetVolume,
        usedMargin,
        tradedVolume,
        orderNo,
        orderPrice,
        orderTime,
        updateTime,
        tradeTime,
    }) {
        
        this.localId = struc.localId;
        this.id = struc.id;
        this.instrument = struc.instrument;
        this.instrumentName = struc.instrumentName;
        this.direction = struc.direction;
        
        /**
         * 1. 由于用户策略采用设置接口进行保存，完全由前端的JSON结构进行控制；
         * 2. 策略ID，暂无需设置；
         * 3. 监控任务 & 策略映射关系，采用策略名称对应
         */
        this.strategyId = undefined;

        this.strategyName = struc.strategyName;
        this.percent = struc.percent;
        this.ceilingPrice = struc.ceilingPrice;
        this.status = struc.status;
        this.hasCanceled = !!struc.hasCanceled;
        this.targetVolume = struc.targetVolume || 0;
        this.usedMargin = struc.usedMargin || 0;
        this.tradedVolume = struc.tradedVolume || 0;
        this.orderNo = struc.orderNo;
        this.orderPrice = struc.orderPrice;
        this.orderTime = struc.orderTime;
        this.flagAsDiscarded = false;
        this.initializeStrategy(struc.strategy);
    }

    /**
     * @param {ZtStandardStrategy} data 
     */
    initializeStrategy(data) {

        /**
         * 当前所使用的策略设置
         */
        this.strategy = data;
    }
}

class SellTask extends BuyTask {

    constructor(params) {
        super(params);
    }
}

var CachedTriggers = {
    
    buys: null,
    cancels: null,
};

class TaskObject {

    constructor(struc) {

        this.id = struc.id;
        this.userId = struc.userId;
        this.userName = struc.userName;
        this.instrument = struc.instrument;
        this.instrumentName = struc.instrumentName;
        this.customStrategy = struc.customStrategy;
        this.direction = struc.direction;
        this.priceFollowType = struc.priceFollowType;
        this.orderInfo = struc.orderInfo || {};
        this.orderNo = struc.orderNo;
        this.orderPrice = struc.orderPrice;
        /** 账号委托数据 */
        this.xorders = [{ accountId: null, accountName: null, volumes: [0] }].splice(1);
        this.limitPositionType = struc.limitPositionType;
        /** auto: 涨停板类型（1：10%涨停，2：20%涨停） */
        this.stockLimitType = struc.stockLimitType;
        /** auto: 打板类型（0：人工打板，1：自动打板） */
        this.strikeBoardType = struc.strikeBoardType;
        this.strikeBoardStatus = struc.strikeBoardStatus;
        this.targetVolume = struc.targetVolume;
        this.tradedVolume = struc.tradedVolume;
        this.supplementTime = struc.supplementTime;
        this.supplementVolume = struc.supplementVolume;
        this.supplementOpen = struc.supplementOpen;
        this.splitInterval = struc.splitInterval;
        this.splitType = struc.splitType;
        this.usedMargin = struc.usedMargin;

        this.splitDetail = {

            main: undefined,
            imbark: undefined,
            protect2: { first: undefined, second: undefined },
            protect3: { first: undefined, second: undefined, third: undefined },

            enableMax: false,
            max: 0,
            decline: false,
            enableMost: false,
            most: 0,
        };

        var sd = struc.splitDetail;
        var sd2 = helper.isJson(sd) ? sd : typeof sd == 'string' && sd.length > 0 ? JSON.parse(sd) : {};
        for (let key in sd2) {
            this.splitDetail[key] = sd2[key];
        }

        this.boardStrategy = {
            
            strategyType: null,
            strategyVolume: null,
            strategyRate: null,
            strategyDelayTime: null,

            // auto: 最低策略数量
            strategyMinVolume: null,

            strategyVolumeOpen: false,
            strategyRateOpen: false,
            sellAmountOpen: false,
            sellAmount: 0,
            sellVolumeOpen: false,
            sellVolume: 0,
            raiseRateLimit: 0,
            raiseOffset: 0,
        };

        var bds = struc.boardStrategy;
        for (let key in bds) {
            this.boardStrategy[key] = bds[key];
        }

        this.cancelCondition = {

            afterLimitTickCount: undefined,
            afterLimitTickEnabled: false,
            beforeTradeCancel: undefined,
            beforeCancelOpen: false,
            downRate: undefined,
            downRateOpen: false,
            extremeBack: undefined,
            extremeBackOpen: false,
            extremeBackTime: undefined,
            lineupOrderVolume: undefined,
            lineupOrderVolumeOpen: false,
            sellOrderVolume: undefined,
            sellOrderVolumeOpen: false,
            customDownRate: undefined,
            customDownRateTime: undefined,
            customDownRateOpen: false,

            // auto: 撤单保护手数
            cancelProtectedVolume: undefined,
            // auto: 撤单保护时间（单位：分钟）
            cancelProtectedTime: undefined,
            // auto: 撤单保护是否启用
            cancelProtectedEnabled: false,

            // auto: 成交保护时间（单位：分钟）
            tradeProtectedTime: undefined,
            // auto: 成交保护是否启用
            tradeProtectedEnabled: false,
            
            // auto: 有效成交额
            effectiveTradedAmount: undefined,
            // auto: 增加补单数量
            supplementOrderVolume: undefined,
            // auto: 补单是否启用
            supplementEnabled: false,
            // auto: 精准跟撤
            followCancel: undefined,
            // auto: 精准跟撤是否启用
            followCancelOpen: false,
            // auto: 是否发生过补单
            hasSupplement: false,
            // auto: 是否已执行撤单
            hasCanceled: false,
        };

        var ccd = struc.cancelCondition;
        for (let key in ccd) {
            this.cancelCondition[key] = ccd[key];
        }

        this.cash = struc.cash;
        this.positionPercent = struc.positionPercent;
        this.creditFlag = struc.creditFlag;

        // 以下，为自动监控有关字段
        
        this.taskId = struc.taskId;
        this.ticketPoolId = struc.ticketPoolId;
        this.ticketPoolName = struc.ticketPoolName;

        // 以上，为自动监控有关字段

        /** 已下单策略携带的订单 */
        this.orderInfo = struc.orderInfo || {};
    }

    static CreateOrders(account_map) {

        if (!account_map) {
            return [];
        }

        var orders = [];

        for (let account_name in account_map) {

            let order_map = account_map[account_name];
            let volumes = [];

            for (let order_id in order_map) {
                volumes.push(Math.ceil(order_map[order_id] * 0.01));
            }

            orders.push({ accountId: null, accountName: account_name, volumes });
        }

        return orders;
    }

    /** 任务是否仅加入到表格中，尚未启动过 */
    static isTaskCreated(status) {

        var ref = TaskStatus;
        return [ref.created].some(x => x.value == status);
    }

    /** 任务是否处于（服务器端已注册）运行状态中 */
    static isTaskRunning(status) {

        var ref = TaskStatus;
        return [ref.started, ref.ordered, ref.supplemented].some(x => x.value == status);
    }

    /** 任务是否已下单，且未完成 */
    static isTaskOrdered(status) {

        var ref = TaskStatus;
        return [ref.ordered].some(x => x.value == status);
    }

    /** 任务是否已补单，且未完成 */
    static isSupplemented(status) {

        var ref = TaskStatus;
        return [ref.supplemented].some(x => x.value == status);
    }

    /** 任务是否已暂停运行 */
    static isTaskPaused(status) {

        var ref = TaskStatus;
        return [ref.stopped].some(x => x.value == status);
    }

    /** 任务是否已完成 */
    static isTaskFinished(status) {

        var ref = TaskStatus;
        return [ref.finished].some(x => x.value == status);
    }

    /** 任务是否已删除 */
    static isTaskDeleted(status) {
        
        var ref = TaskStatus;
        return [ref.deleted].some(x => x.value == status);
    }

    /** 任务是否可被删除 */
    static isTaskDeletable(status) {

        var ref = TaskStatus;
        return [ref.created, ref.deleted, ref.finished, ref.stopped].some(x => x.value == status);
    }

    /**
     * @param {TaskObject} data 
     * @param {Array<BuyCancelTrigger>} buys 
     * @param {Array<BuyCancelTrigger>} cancels 
     */
    static Convert2BuyTask(data, buys, cancels) {
        
        var btask = new BuyTask({

            localId: '0' + data.id,
            id: data.id,
            instrument: data.instrument,
            instrumentName: data.instrumentName,
            direction: data.direction,
            strategyId: null,
            strategyName: data.customStrategy,
            percent: null,
            ceilingPrice: null,
            status: data.strikeBoardStatus,
            targetVolume: data.targetVolume,
            tradedVolume: data.tradedVolume,
            usedMargin: data.usedMargin,
            orderNo: data.orderNo,
            orderPrice: data.orderPrice,
            orderTime: data.orderTime,
            hasCanceled: !!data.cancelCondition.hasCanceled,
        });

        // 买入触发参数
        var mbs = data.boardStrategy;
        // 扩充触发条件参数项
        buys.forEach(trg => {
            trg.checked = !!mbs[trg.variable];
            trg.conditions.forEach(cdt => {
                let thr = mbs[cdt.variable];
                cdt.threshold = typeof thr == 'number' ? thr / (cdt.times || 1) : thr;
            });
        });

        // 撤单触发参数
        var mcc = data.cancelCondition;
        // 扩充触发条件参数项
        cancels.forEach(trg => {
            trg.checked = !!mcc[trg.variable];
            trg.conditions.forEach(cdt => {
                let thr = mcc[cdt.variable];
                cdt.threshold = typeof thr == 'number' ? thr / (cdt.times || 1) : thr;
            });
        });

        /**
         * 服务器端返回的拆单结构，为json的字符串格式
         */
        
        var msd = data.splitDetail;
        if (typeof msd == 'string') {
            msd = JSON.parse(msd);
        }
        
        btask.strategy = {

            name: data.customStrategy,
            isTemp: data.customStrategy == '临时策略',
            btriggers: buys,
            ctriggers: cancels,
            limit: {

                max: { checked: !!msd.enableMax, volume: msd.max },
                topmost: { checked: !!msd.enableMost, volume: Math.round((msd.most || 0) * 0.01) },
                decrease: { checked: !!msd.decline },
                position: {

                    method: data.limitPositionType, 
                    percentage: data.positionPercent || 0,
                    amount: +((data.cash || 0) / 10000).toFixed(2),
                },
                creditFlag: !!data.creditFlag,
            },
        };

        return btask;
    }

    /**
     * @returns {{
     * buys: Array<BuyCancelTrigger>, 
     * cancels: Array<BuyCancelTrigger>,
     * }}
     */
    static transerTriggers(login_input) {

        if (CachedTriggers.buys && CachedTriggers.cancels) {
            return helper.deepClone(CachedTriggers);
        }

        let raw_str = login_input.rawConfigStr;
        let config = null;

        if (typeof raw_str == 'string' && raw_str.length > 0) {

            try {
                config = JSON.parse(raw_str);
            }
            catch(ex) {
                console.log(raw_str);
                console.error('strategy raw config converting error', ex);
            }
        }

        let buys = [];
        let cancels = [];

        let { buy, cancel } = config || {};
        let is_ok = helper.isJson(config) && buy instanceof Array && buy.length > 0 && cancel instanceof Array && cancel.length > 0;
        if (is_ok) {

            buy.forEach(item => { buys.push(new BuyCancelTrigger(item)); });
            cancel.forEach(item => { cancels.push(new BuyCancelTrigger(item)); });
        }

        return (CachedTriggers = {
            buys,
            cancels,
        });
    }
}

/**
 * 监控任务来源
 */
const FromTable = {

    /** 左上侧任务列表 */
    added: 1, 
    /** 左下侧任务列表 */
    canceled: 2,
    /** 右上侧任务列表 */
    ordered: 3,
    /** 右下侧任务列表 */
    ordered2: 4,
};

/**
 * 监控任务数据行操作行为
 */
const RowBehaviors = {

    /** 选中数据行 */
    select: 1,
    /** 删除数据行 */
    delete: 2,
};

/**
 * @param {BuyCancelTrigger} data 
 */
function asBuyCancelTrigger(data) {
    return data;
}

/**
 * @param {ZtStandardStrategy} remote 
 * @param {Array<BuyCancelTrigger>} buy_triggers 
 * @param {Array<BuyCancelTrigger>} cancel_triggers
 */
function mergeLocalAndRemoteStrategies(remote, buy_triggers, cancel_triggers) {

    /**
     * 删除本地版本中，不存在的条目
     */

    let local_b_variables = buy_triggers.map(x => x.variable);
    let local_c_variables = cancel_triggers.map(x => x.variable);
    remote.btriggers.remove(item => local_b_variables.indexOf(asBuyCancelTrigger(item).variable) < 0);
    remote.ctriggers.remove(item => local_c_variables.indexOf(asBuyCancelTrigger(item).variable) < 0);

    /**
     * 查润本地版本，新增的条目
     */

    let remote_b_variables = remote.btriggers.map(x => x.variable);
    let remote_c_variables = remote.ctriggers.map(x => x.variable);
    let new_b_items = buy_triggers.filter(item => remote_b_variables.indexOf(item.variable) < 0);
    let new_c_items = cancel_triggers.filter(item => remote_c_variables.indexOf(item.variable) < 0);
    remote.btriggers.push(...helper.deepClone(new_b_items));
    remote.ctriggers.push(...helper.deepClone(new_c_items));

    let sample = new CriteriaParam({});
    let sampleKeys = Object.keys(sample);
    [...remote.btriggers, ...remote.ctriggers].forEach(item => {
        item.conditions.forEach(cdt => {
            let keys = Object.keys(cdt);
            let newKeys = sampleKeys.filter(key => keys.indexOf(key) < 0);
            newKeys.forEach(key => { cdt[key] = sample[key]; });
        });
    });

    /**
     * 对合并后的触发项进行排序
     */
    remote.btriggers.sort((x, y) => {

        let max_idx = 9999;
        let x_idx = local_b_variables.indexOf(x.variable);
        let y_idx = local_b_variables.indexOf(y.variable);
        if (x_idx == -1) {
            x_idx = max_idx;
        }
        if (y_idx == -1) {
            y_idx = max_idx;
        }
        return helper.compare(x_idx, y_idx, true);
    });

    remote.ctriggers.sort((x, y) => {

        let max_idx = 9999;
        let x_idx = local_c_variables.indexOf(x.variable);
        let y_idx = local_c_variables.indexOf(y.variable);
        if (x_idx == -1) {
            x_idx = max_idx;
        }
        if (y_idx == -1) {
            y_idx = max_idx;
        }
        return helper.compare(x_idx, y_idx, true);
    });
}

module.exports = {
    
    TaskObject,
    BuyTask,
    SellTask,
    LabelValue,
    ShortcutConfig,
    CriteriaParam,
    BuyCancelTrigger,
    ZtSetting,
    ZtStandardStrategy,

    TaskStatus,
    TaskStatuses,
    Digits,
    FunctionKeys,
    KeyStrokes4Buy,
    KeyStrokes4Sell,
    Entrance,
    FromTable,
    RowBehaviors,
    mergeLocalAndRemoteStrategies,
};