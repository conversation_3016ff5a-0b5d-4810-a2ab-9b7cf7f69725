﻿/**
 * message function code definition (send from local to server)
 */

const serverFunction = {

    heartBeat: 1,
    ping: 8,
    userLogin: 10000,
    userLoginQuote: 10000,
    sendOrder: 10001,
    cancelOrder: 10002,
    strategyAdjustPosition: 10003,
    sendMotherOrder: 10035,
    sendBasketOrder: 10007,
    suspendBasket: 10008,
    resetBasket: 10009,
    cancelBasketOrder: 10010,
    confirmBasket: 10011,

    subscribeTick: 11000,
    unsubscribeTick: 11001,
    refreshToken: 11002,
    subAccountChange: 11005,
    unsubAccountChange: 11006,
    userLogout: 19999,

    // todo19: broker use the code, but it causes conflict here
    auditOrder: 11010,
    subscribeTradableList: 11007,

    requestTodayOrder: 10030,
    requestTodayTradeRecord: 10031,
    requestTodayPosition: 10032,

    /** 请求账号权益数据 */
    requestAccountEquity: 10033,
    /** 请求账号持仓数据 */
    requestAccountPosition: 10034,
    /** 请求批量订单(母单) */
    requestMotherOrder: 10022,
    /** 追单 */
    replaceOrder: 10100,
    /** 划拨资金 */
    allocateCash: 13,
};

module.exports = { serverFunction };
