const IView = require('../component/iview').IView;
const path = require('path');
const fs = require('fs');
const IpLib = require('ip');
const svgCaptcha = require('../libs/3rd/svg-captcha');
const { app } = require('@electron/remote');
const { encryptAuthFile } = app.encryptionOptions;
const { LocalFileStorage } = require('../toolset/local-file-storage');
const { encodeString } = require('./encoder');
const { isDev } = require('../config/environment');

class View extends IView {

    get macLib() {
        return this._macLib || (this._macLib = require('getmac'));
    }

    get logR() {
        return this.systemUserEnum.loginResult;
    }

    get isHttps() {

        try {
            return this.app.GeneralSettings.https;
        }
        catch(ex) {
            return false;
        }
    }

    get isCombinedMarket() {

        try {
            return this.app.GeneralSettings.combinedMarket;
        }
        catch(ex) {
            return false;
        }
    }

    constructor(view_name) {

        super(view_name, true, '用户登录');

        this.vueApp = null;
        this.storageKeys = {
            rem_usr_name: 'rem_usr_name',
            recent_server: 'recent_server',
            recent_user_name: 'recent_user_name',
            serverInfo: 'serverInfo',
        };
        this.states = {

            trdPassed: null,
            quotePassed: null,
        };

        this.servers = [];

        this.captchaOption = {

            size: 4, // 验证码长度为 4 个字符
            ignoreChars: '0o1iIlL', // 忽略容易混淆的字符
            noise: 0, // 添加 0 条噪声线
            color: true, // 启用彩色字符
            background: '#ffffff', // 设置较淡的背景色
            width: 150, // 验证码图片宽度
            height: 50, // 验证码图片高度
            fontSize: 50, // 字体大小
            charPreset: 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnopqrstuvwxyz23456789' // 自定义字符集（排除容易混淆的字符）
        };

        this.uidata = {

            userName: null,
            userNameError: null,
            passcode: null,
            passcodeError: null,
            captcha: null,
            captchaError: null,
            rememberUserName: this.getRememberUserNameOption(),
            isSigningIn: false,
            /** 是否为本地加密密码 */
            isLocalEncryptedPwd: false,
            servers: this.servers,
            selectedServer: this.getRecentServerProfileId(),
        };

        if (this.uidata.rememberUserName) {
            this.uidata.userName = this.getRecentUser();
        }
    }

    getRememberUserNameOption() {
        return LocalFileStorage.getItem(this.storageKeys.rem_usr_name) == 1;
    }

    setRememberUserNameOption(yes) {
        LocalFileStorage.setItem(this.storageKeys.rem_usr_name, !!yes ? 1 : 0);
    }

    getRecentServerProfileId() {

        var recent_id = LocalFileStorage.getItem(this.storageKeys.recent_server);
        var matched = this.servers.first(x => {
            return x.id === recent_id;
        });
        return matched ? matched.id : null;
    }

    setRecentServerProfileId(server_profile_id) {
        LocalFileStorage.setItem(this.storageKeys.recent_server, server_profile_id);
    }

    getRecentUser() {

        let user_name = LocalFileStorage.getItem(this.storageKeys.recent_user_name);
        return typeof user_name == 'string' ? user_name.trim() : null;
    }

    setRecentUser(user_name) {

        let key = this.storageKeys.recent_user_name;
        if (user_name) {
            LocalFileStorage.setItem(key, user_name);
        }
        else {
            LocalFileStorage.removeItem(key);
        }
    }

    setUserNameError() {
        this.uidata.userNameError = !this.uidata.userName ? '用户名未输入' : null;
    }

    setPasscodeError() {
        this.uidata.passcodeError = !this.uidata.passcode ? '密码未输入' : null;
    }

    setCaptchaError() {
        this.uidata.captchaError = !this.uidata.captcha ? '验证码未输入' : (this.uidata.captcha || '').toLowerCase() !== this.validation.text.toLowerCase() ? '验证码错误' : null;
    }

    createApp($container) {
        this.vueApp = new Vue({
            el: $container,
            data: this.uidata,

            watch: {
                rememberUserName: yes => {
                    this.setRememberUserNameOption(yes);
                },
            },

            computed: {
                hasUserNameError: () => {
                    return !!this.uidata.userNameError;
                },
                hasPasscodeError: () => {
                    return !!this.uidata.passcodeError;
                },
                hasCaptchaError: () => {
                    return !!this.uidata.captchaError;
                },
            },

            methods: {
                closeWindow: () => {
                    this.thisWindow.close();
                },
                checkUserNameInput: () => {
                    this.setUserNameError();
                },
                checkPasscodeInput: () => {
                    this.setPasscodeError();
                },
                checkCaptchaInput: () => {
                    this.setCaptchaError();
                },
                refreshCaptcha: () => {
                    this.resetCaptcha();
                },
                toSignIn: () => {
                    this.checkAndSignIn();
                },
                move2Next: e => {
                    e.keyCode === 13 ? document.getElementById('input-user-passcode').focus() : null;
                },
                finishInput: e => {
                    e.keyCode === 13 ? document.getElementById('btn-to-sign-in').click() : null;
                },
            },
        });

        this.vueApp.$nextTick(() => {
            this.resetCaptcha();
        });
    }

    resetCaptcha() {
        this.validation = svgCaptcha.create(this.captchaOption);
        document.getElementById('captcha-img').innerHTML = this.validation.data;
    }

    async getDiskSN() {

        if (this._diskSN !== undefined) {
            return this._diskSN;
        }
        
        try {
            let si = require('systeminformation');
            let results = await si.diskLayout();
            this._diskSN = results.length > 0 ? results[0].serialNum : 'N/A';
        }
        catch(ex) {
            this._diskSN = 'N/A';
        }
        finally {
            return this._diskSN;
        }
    }

    getStrategyConfig(signin_token) {

        let app_path = process.cwd();
        let authentic_file_1 = path.join(app_path, signin_token);
        let authentic_file_2 = path.join(app_path, 'auth-files', signin_token);
        let has_file_1 = fs.existsSync(authentic_file_1);
        let has_file_2 = fs.existsSync(authentic_file_2);
        let absence = '';

        if (!has_file_1 && !has_file_2) {
            return { raw: absence, encrypted: absence };
        }

        try {

            let authentic_file = has_file_1 ? authentic_file_1 : authentic_file_2;
            let content = fs.readFileSync(authentic_file, { encoding: 'utf8' });
            let has_config = typeof content == 'string' && content.length > 0;
            if (has_config && encryptAuthFile) {
                content = this.helper.ununicode(this.helper.aesDecrypt(content));
            }
            let converted = has_config ? this.helper.tomd5(signin_token + content) : absence;
            return {
                raw: has_config ? content : absence,
                encrypted: has_config ? converted : absence,
            };
        }
        catch (ex) {
            return { raw: absence, encrypted: absence };
        }
    }

    checkAndSignIn() {

        if (isDev) {
            this.uidata.captcha = this.validation.text;
        }

        if (!this.uidata.userName || !this.uidata.passcode || (this.uidata.captcha || '').toLowerCase() !== this.validation.text.toLowerCase()) {

            this.setUserNameError();
            this.setPasscodeError();
            this.setCaptchaError();
            return;
        }
        else if (this.uidata.selectedServer == null || this.uidata.selectedServer == '') {
            
            this.interaction.showError('请选择服务器');
            return;
        }

        if (this.uidata.rememberUserName) {
            this.setRecentUser(this.uidata.userName);
        }

        let selected_server = this.servers.first(x => x.id == this.uidata.selectedServer);
        this.resetUIStatus(null);
        this.resetDisconnectionReason();
        this.setSelectedServer(selected_server);
        this.setRecentServerProfileId(this.uidata.selectedServer);
        this.resetCaptcha();
        this.uidata.isSigningIn = true;

        this.macLib.getMac(async (err, mac_addr) => {

            var platform = require('os').platform();
            var disk_sn = await this.getDiskSN();
            var internal_ip = IpLib.address();
            var sconfig = this.getStrategyConfig((this.uidata.userName || '').trim());
            var user_input = {

                userName: this.uidata.userName,
                passCode: this.uidata.isLocalEncryptedPwd ? this.uidata.passcode : encodeString(this.uidata.passcode),
                os: `${ platform }|${ disk_sn }|${ internal_ip }`,
                macAddr: err ? '' : mac_addr,
                configStr: sconfig.encrypted,
                rawConfigStr: sconfig.raw,
            };

            if (!user_input.macAddr) {
                this.loggerSys.error('MAC addr not fetched');
            }

            if (user_input.macAddr && typeof user_input.macAddr != 'string') {
                user_input.macAddr = user_input.macAddr.toUpperCase();
            }

            let cloned_input = Object.assign({}, user_input);
            delete cloned_input.passCode;
            this.setLoginInput(user_input, disk_sn);
            this.loggerSys.debug(`sign-in window > to login to server: ${JSON.stringify(selected_server)} with input: ${JSON.stringify(cloned_input)}`);
            this.doSignIn();
        });
    }

    resetUIStatus(error_msg) {

        this.states.trdPassed = null;
        this.states.quotePassed = null;

        this.uidata.userNameError = null;
        this.uidata.passcodeError = null;
        this.uidata.isSigningIn = false;

        this.uidata.userNameError = error_msg;
        this.uidata.passcodeError = null;
    }

    resetDisconnectionReason() {

        this.app.contextData.disconnectedByLogout = null;
        this.app.contextData.disconnectedTradingServerAccidently = null;
        this.app.contextData.disconnectedQuoteServerAccidently = null;
    }

    setSelectedServer(server_info) {

        LocalFileStorage.setItem(this.storageKeys.serverInfo, server_info);
        this.app.contextData.serverInfo = server_info;
    }

    setLoginInput(user_input, disk_sn) {
        
        this.app.contextData.logInInput = user_input;
        this.app.contextData.diskSN = disk_sn;
    }

    doSignIn() {
        // only login successfully onto [trading] server, and then can quote server tries to login <if required depending on user role>
        this.renderProcess.send(this.systemEvent.toLoginTradingServer);
    }

    quoteServerNotRequired(signin_response) {
        // return signin_response.roleId === this.systemUserEnum.userRole.superAdmin.code;
        return true;
    }

    listen2TradingServerLoginCompletement() {

        this.renderProcess.on(this.systemEvent.loginTradingServerCompleted, (event, signin_response) => {

            this.states.trdPassed = signin_response.errorCode === this.logR.ok.code;
            if (!this.states.trdPassed) {
                this.uidata.isSigningIn = false;
            }

            // only when login onto trading server is validated ok, then can try to login onto quote server (if required for this user type)
            if (this.states.trdPassed && this.quoteServerNotRequired(signin_response)) {
                this.loggerSys.debug(`sign-in window > user with username = ${signin_response.userName}, fullname = ${signin_response.fullName} does not require quote server`);
                // bypass login onto quote server
                this.states.quotePassed = true;
                this.handleSignInResponse(signin_response, '交易服务器');
                return;
            }

            /*
			    set trading server login feedback,
			    no matter [ok] or [error]
			    and it will be 100% replaced with latest quote server login feeback very quickly (if with error).
			*/
            this.handleSignInResponse(signin_response, '交易服务器');

            if (this.states.trdPassed) {
                this.renderProcess.send(this.systemEvent.toLoginQuoteServer);
            }
        });
    }

    listen2QuoteServerLoginCompletement() {

        this.renderProcess.on(this.systemEvent.loginQuoteServerCompleted, (event, signin_response) => {
            this.states.quotePassed = signin_response.errorCode === this.logR.ok.code;
            this.handleSignInResponse(signin_response, '行情服务器');
        });
    }

    listen2NetworkEvents() {
        /*
		    the followed 3 methods will be fired by [trading] server or [quote] server for aligned event
		    no matter which connection happens to be [FAILED],
            the whole login process is treated as [FAILED]
		*/

        this.renderProcess.on(this.systemEvent.connTimedOut, (event, error_msg) => {
            this.resetUIStatus(error_msg);
        });
        this.renderProcess.on(this.systemEvent.connError, (event, error_msg) => {
            this.resetUIStatus(error_msg);
        });
        this.renderProcess.on(this.systemEvent.connClosed, (event, error_msg) => {
            this.resetUIStatus(error_msg);
        });
    }

    listen2AdminKicksMeOut() {
        this.renderProcess.on(this.serverEvent.forcedKickOut, () => {
            alert('您已被管理员强制下线');
        });
    }

    handleSignInResponse(signin_response, server_type_name) {

        if (!this.thisWindow.isVisible()) {
            return;
        }

        var error_code = signin_response.errorCode;

        // both [trading] & [quote] server login finished(ok / failed)
        if (this.states.trdPassed != null && this.states.quotePassed != null) {
            this.uidata.isSigningIn = false;
        }

        // both [trading] & [quote] servers login successfully
        if (this.states.trdPassed === true && this.states.quotePassed === true) {

            this.uidata.userNameError = null;
            this.uidata.passcodeError = null;
            this.renderProcess.send(this.systemEvent.loginRequestValidatedOk);
            return;
        }

        // 交易服务器，或行情服务器，其中一方已经完成，另外一方登录尚未返回
        if (error_code === this.logR.ok.code) {
            return;
        }

        switch (error_code) {

            case this.logR.userNameOrPasscodeError.code:
                this.uidata.userNameError = `(${server_type_name}) ${this.logR.userNameOrPasscodeError.mean}`;
                break;

            case this.logR.userNameNonExist.code:
                this.uidata.userNameError = `(${server_type_name}) ${this.logR.userNameNonExist.mean}`;
                break;

            case this.logR.passcodeError.code:
                this.uidata.passcodeError = `(${server_type_name}) ${this.logR.passcodeError.mean}`;
                break;

            case this.logR.userDisabled.code:
                this.uidata.userNameError = `(${server_type_name}) ${this.logR.userDisabled.mean}`;
                break;

            case this.logR.alreadySignedIn.code:
                this.uidata.userNameError = `(${server_type_name}) ${this.logR.alreadySignedIn.mean}`;
                break;

            default:
                this.uidata.userNameError = `(${server_type_name}) ${signin_response.errorMsg || '未知错误'}`;
                this.uidata.passcodeError = null;
                break;
        }
    }

    toExitApp() {

        this.renderProcess.send(this.systemEvent.exitApp);
        this.thisWindow.close();
    }

    bindServers() {

        try {

            var app_path = process.cwd();
            var config_file = path.join(app_path, 'servers');

            if (!fs.existsSync(config_file)) {

                alert('服务器配置文件缺失，请确认：' + config_file);
                this.toExitApp();
                return;
            }

            var server_cfgs = [];
            try {

                const list = require(config_file);
                if (list instanceof Array && list.length > 0) {
                    server_cfgs.merge(list);
                }
            }
            catch (ex) {

                alert('导入服务器配置产生异常，终止运行。');
                this.toExitApp();
                return;
            }

            if (server_cfgs.length == 0) {

                alert('服务器配置，未指定任何可用服务器列表，终止运行。');
                this.toExitApp();
                return;
            }
            else {

                /**
                 * 合并沪深市场的接口时，将深圳强制设置为沪市相同（无论是否有设置深圳）
                 */

                if (this.isCombinedMarket) {

                    server_cfgs.forEach(item => {
                        item.servers.restServerSz = item.servers.restServer;
                    });
                }

                let invalid = server_cfgs.find(x => {

                    let server_name = x.serverName;
                    if (typeof server_name != 'string' || server_name.trim().length == 0) {
                        return true;
                    }

                    let servers = x.servers;            
                    return typeof servers.dataServer != 'string' || servers.dataServer.indexOf(':') <= 0
                        || typeof servers.quoteServer != 'string' || servers.quoteServer.indexOf(':') <= 0
                        || typeof servers.quoteRestfulServer != 'string' || servers.quoteRestfulServer.indexOf(':') <= 0
                        || typeof servers.restServer != 'string' || servers.restServer.indexOf(':') <= 0
                        || typeof servers.restServerSz != 'string' || servers.restServerSz.indexOf(':') <= 0
                        || typeof servers.tradeServer != 'string' || servers.tradeServer.indexOf(':') <= 0;
                });

                if (invalid) {

                    alert('服务器配置，非预期数据结构，终止运行。');
                    this.toExitApp();
                    return;
                }
            }

            let prefix = this.isHttps ? 'https://' : 'http://';
            let counter = 1;
            let dto_servers = server_cfgs.map(cfg => {

                let server_name = cfg.serverName;
                let servers = cfg.servers;
                let trade_server = servers.tradeServer.split(':');
                let quote_server = servers.quoteServer.split(':');

                return {

                    id: ++counter,
                    name: server_name,
                    tradingServer: { ip: trade_server[0], port: trade_server[1] },
                    quoteServer: { ip: quote_server[0], port: quote_server[1] },
                    quoteRestfulServer: `http://${ servers.quoteRestfulServer }`,
                    restfulServer: `${ prefix }${ servers.restServer }/quant/v3`,
                    restfulServerSz: `${ prefix }${ servers.restServerSz }/quant/v3`,
                    historyServer: `${ prefix }${ servers.dataServer }/quote/v3`,
                    indayServer: 'http://local.gaoyusoft.com:8181',
                };
            });

            this.servers.clear();
            this.servers.merge(dto_servers);

            if (this.servers.length > 0) {
                this.uidata.selectedServer = this.getRecentServerProfileId();
            }
        }
        catch (e) {
            this.interaction.showError('获取服务器列表异常!');
        }
    }

    bindLocalPwd() {
            
        var app_path = process.cwd();
        var pwd_file = path.join(app_path, 'encrypted-user-password.txt');

        if (fs.existsSync(pwd_file)) {
            
            let content = fs.readFileSync(pwd_file, { encoding: 'utf8' });
            let has_config = typeof content == 'string' && content.length > 0;
            if (has_config) {

                this.uidata.isLocalEncryptedPwd = true;
                this.uidata.passcode = content.trim();
            }
        }
    }

    build($container) {

        this.listen2TradingServerLoginCompletement();
        this.listen2QuoteServerLoginCompletement();
        this.listen2NetworkEvents();
        this.listen2AdminKicksMeOut();
        this.createApp($container);
        this.bindServers();
        this.bindLocalPwd();
        this.getDiskSN();
    }
}

module.exports = View;