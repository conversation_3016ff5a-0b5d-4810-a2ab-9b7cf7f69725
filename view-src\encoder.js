const path = require('path');
const fs = require('fs');
const koffi = require('koffi');

/**
 * @param {string} content 
 */
function encodeString(content) {

    const fpath = path.join(process.cwd(), 'encode/Dogskin.dll');
    if (!fs.existsSync(fpath)) {
        return content;
    }
    
    try {

        const lib = koffi.load(fpath);
        const MethodRef = lib.func('__stdcall', 'Encode', 'char *', ['char *']);
        const encrypted = MethodRef(content);
        return encrypted;
    }
    catch (ex) {
        return content;
    }
}

module.exports = { encodeString };