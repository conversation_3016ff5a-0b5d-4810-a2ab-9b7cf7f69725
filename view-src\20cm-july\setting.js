const { IView } = require('../../component/iview');
const { ZtSetting } = require('./objects');

module.exports = class SettingView extends IView {

    constructor() {

        super('@20cm-july/setting', false, '涨停交易设置');
        this.ztsetting = ZtSetting.makeDefault();
        this.strategies = [];
        this.dialog = { visible: true };
        this.states = { isThsSupported: false };
        this.localShortcutSetting = {
            enabled: false, 
            hasKey1: false, 
            hasKey2: false,
            hasKey3: false,
            hasKey4: false,
            hasKey5: false,
        };
    }

    affectShortcuts(enabled, hasKey1, hasKey2, hasKey3, hasKey4, hasKey5) {
        Object.assign(this.localShortcutSetting, { enabled, hasKey1, hasKey2, hasKey3, hasKey4, hasKey5 });
    }

    createApp() {

        new Vue({

            el: this.$container.firstElementChild,
            data: {

                dialog: this.dialog,
                setting: this.ztsetting,
                strategies: this.strategies,
                states: this.states,
                shortcuts: this.localShortcutSetting,
                levels: [
                  {
                    label: '卖一',
                    value: 0
                  },
                  {
                    label: '卖二',
                    value: 1
                  },
                  {
                    label: '卖三',
                    value: 2
                  },
                  {
                    label: '卖四',
                    value: 3
                  },
                  {
                    label: '卖五',
                    value: 4
                  },
                ]
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.close,
                this.cancel,
                this.save,
                this.handleKeyDown,
                this.handleBuyKeyDown,
                this.clearShortCancel,
                this.clearShortBuy
            ]),
        });
    }


    handleKeyDown(e) {
      e.preventDefault();
      // 检查按键是否是 F1-F12
      if (e.key.startsWith('F') && e.key.length > 1) {
        const fNumber = parseInt(e.key.substring(1))
        if (fNumber >= 1 && fNumber <= 12) {
          if (e.key == this.ztsetting.buy.stroke) {
            this.interaction.showError('和买入快捷键冲突');
          } else {
            this.ztsetting.cancelStroke = e.key;
          }
        }
      }
    }

    handleBuyKeyDown(e) {
      e.preventDefault();
      // 检查按键是否是 F1-F12
      if (e.key.startsWith('F') && e.key.length > 1) {
        const fNumber = parseInt(e.key.substring(1))
        if (fNumber >= 1 && fNumber <= 12) {
          if (e.key == this.ztsetting.cancelStroke) {
            this.interaction.showError('和撤单快捷键冲突');
          } else {
            this.ztsetting.buy.stroke = e.key;
          }
        }
      }
    }

    clearShortCancel() {
      this.ztsetting.cancelStroke = null;
    }

    clearShortBuy() {
      this.ztsetting.buy.stroke = null;
    }


    close() {
        this.dialog.visible = false;
    }

    cancel() {
        this.close();
    }

    save() {

        this.trigger('ztsetting-change', this.ztsetting);
        this.close();
    }

    exposeThsChoice() {
        this.states.isThsSupported = true;
    }

    /**
     * @param {Array<string>} strategies
     */
    setStrategies(strategies) {

        this.strategies.refill(strategies);

        /**
         * 反向判断是否有对应名称的策略
         */
        setTimeout(() => {

            if (!strategies.some(x => x == this.ztsetting.ths.strategy)) {
                this.ztsetting.ths.strategy = null;
            }

            if (!strategies.some(x => x == this.ztsetting.ths.strategy2)) {
                this.ztsetting.ths.strategy2 = null;
            }

            if (!strategies.some(x => x == this.ztsetting.ths.strategy3)) {
                this.ztsetting.ths.strategy3 = null;
            }

            if (!strategies.some(x => x == this.ztsetting.ths.strategy4)) {
                this.ztsetting.ths.strategy4 = null;
            }

            if (!strategies.some(x => x == this.ztsetting.ths.strategy5)) {
                this.ztsetting.ths.strategy5 = null;
            }

        }, 200);
    }

    showup() {
        this.dialog.visible = true;
    }

    /**
     * @param {ZtSetting} latest
     */
    update2Latest(latest) {

        if (this.helper.isJson(latest)) {

            const ref = this.ztsetting;
            const ths_latest = latest.ths || {};
            ref.biggerFont = !!latest.biggerFont;
            ref.doubleClick2Cancel = !!latest.doubleClick2Cancel;
            ref.ths.manualConfirm = !!ths_latest.manualConfirm;
            ref.ths.immediate = !!ths_latest.immediate;
            ref.ths.strategy = ths_latest.strategy;
            ref.ths.strategy2 = ths_latest.strategy2;
            ref.ths.strategy3 = ths_latest.strategy3;
            ref.ths.strategy4 = ths_latest.strategy4;
            ref.ths.strategy5 = ths_latest.strategy5;
            ref.cancelStroke = latest.cancelStroke;
            ref.buy = latest.buy;
        }
    }

    build($container) {

        super.build($container);
        this.createApp();
    }
};
