const Cm20FunctionCodes = {

    request: {

        start: 13001,
        modify: 13002,
        query: 13003,
        delete: 13004,
        cancel: 13005,
        stop: 13006,
        mannualBuy: 13007,
    },

    reply: {

        started: 21001,
        modified: 21002,
        queried: 21003,
        deleted: 21004,
        canceled: 21005,
        stopped: 21006,
        mannualBuy: 21007,
    },

    notify: {

        created: 31001, // 已启动
        changed: 31002, // 人工更改已处理，或委托成交变化
        deleted: 31003, // 已删除（仅有ID字段）
    },

    auto: {

        start: 14000,
        stop: 14001,
        start_resp: 24000,
        stop_resp: 24001,

        auto_created: 32001, // 自动监控，已启动
        auto_changed: 32002, // 自动监控，已发生变更
        auto_deleted: 32003, // 自动监控，已删除

        strategy_started: 33000, // 策略已启动，通知消息到达
        strategy_stopped: 33001, // 策略已停止，通知消息到达
    },
};

module.exports = {

    Cm20FunctionCodes,
};