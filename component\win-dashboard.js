const BaseWindow = require('./base-window').BaseWindow;
const TabList = require('./tab-list').TabList;
const Tab = require('./tab').Tab;
const Menu = require('../model/menu').Menu;
const repoTradingDay = require('../repository/trading-day').repoTradingDay;

class WinDashboard extends BaseWindow {

    constructor() {
        
        super('@win-dashboard', true);
        this.serverInfo = this.getContextDataItem(this.dataKey.serverInfo);
    }

    toggleShowMenu() {

        if (this.menuStates.expanded) {

            this.menuStates.expanded = false;
            this.$menuToggleCrumb.style.display = 'block';
            document.body.classList.remove(this.classes.menuOn);
        }
        else {

            this.menuStates.expanded = true;
            this.$menuToggleCrumb.style.display = 'none';
            document.body.classList.add(this.classes.menuOn);
        }
    }

    /**
     * @param {Menu} menu 
     */
    handleLevel1MenuClick(menu) {

        this.menuStates.selectedLevel1 = menu;
        if (menu.children.length == 0) {
            this.openTabViewByMenu(menu);
        }
        else {
            menu.expanded = !menu.expanded;
        }
    }

    /**
     * @param {Menu} sub_menu 
     */
    handleLevel2MenuClick(sub_menu) {

        this.menuStates.selectedLevel1 = this.menus.find(x => x.menuId == sub_menu.parentId);
        this.menuStates.selectedLevel2 = sub_menu;
        this.openTabViewByMenu(sub_menu);
    }

    createMenuApp() {

        var $side = document.querySelector('.win-side');
        this.menuApp = new Vue({

            el: $side,
            data: {
                menus: this.menus,
                states: this.menuStates,
            },
            methods: this.helper.fakeVueInsMethod(this, [this.toggleShowMenu, this.handleLevel1MenuClick, this.handleLevel2MenuClick]),
        });
    }

    createWinButtonsApp() {

        this.winButtonsApp = new Vue({

            el: document.querySelector('.win-header > .win-buttons'),
            data: {

                winStates: this.winStates,
                userName: this.userInfo.userName,
                isUserSigninMode: this.isUserSigninMode,
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.minimize, 
                this.maximize, 
                this.unmaximize, 
                this.lockWindow,
                this.handleUserCommand, 
                this.close,
            ]),
        });
    }

    selectMenu(id) {

        let matched = this.menus.find(x => x.menuId == id || x.children.some(y => y.menuId == id));
        if (matched == undefined) {
            return;
        }
        
        let is_level1 = matched.menuId == id;
        if (is_level1) {

            this.menuStates.selectedLevel1 = matched;
            this.menuStates.selectedLevel2 = null;
        }
        else {

            matched.expanded = true;
            let child = matched.children.find(x => x.menuId == id);
            this.menuStates.selectedLevel1 = matched;
            this.menuStates.selectedLevel2 = child;
        }
    }

    /**
     * @param {Tab} tab 
     */
    handleTabCreated(tab) {

        let { menuId, menuName } = tab.viewOptions;
        this.selectMenu(menuId);
    }

    /**
     * @param {Tab} tab 
     */
    handleTabFocused(tab) {

        let { menuId, menuName } = tab.viewOptions;
        this.selectMenu(menuId);
    }

    /**
     * @param {Tab} tab 
     */
    handleTabUnfocused(tab) {
        //
    }

    /**
     * @param {Tab} tab 
     */
    handleTabClosed(tab) {
        //
    }

    setupTabCtr() {

        var $navi = document.querySelector('.win-content > .tab-placeholder > .win-top-tabs');
        var $content = document.querySelector('.win-content > .win-top-content');
        this.toptab = new TabList({

            $navi: $navi,
            $content: $content,
            lazyLoad: false,
            hideTab4OnlyOne: false,
            tabCreated: this.handleTabCreated.bind(this),
            tabFocused: this.handleTabFocused.bind(this),
            tabUnfocused: this.handleTabUnfocused.bind(this),
            tabClosed: this.handleTabClosed.bind(this),
        });
    }

    logout() {

        this.interaction.showConfirm({

            title: '操作确认',
            message: '确定要注销吗？',
            confirmed: () => {
                this.renderProcess.send(this.systemEvent.toLogout);
            },
        });
    }

    handleUserCommand(command) {
        
        switch (command) {

            case 'logout': this.logout(); break;
            case 'modify-password': this.modifyPassword(); break;
        }
    }

    modifyPassword() {

        if (this.mpView) {

            this.mpView.dialog.visible = true;
            return;
        }

        const { isDev } = require('../config/environment');
        var path = isDev ? '../view-src' : '../view';
        var ModifyPasswordView = require(path + '/modify-password');
        // var ModifyPasswordView = require('../view-src/modify-password');
        var dialog = new ModifyPasswordView();
        dialog.loadBuild(document.body.querySelector('.win-content'));
        this.mpView = dialog;
    }

    lockWindow() {
        this.locker.lock();
    }

    close() {

        this.interaction.showConfirm({

            title: '操作确认',
            message: '是否确认退出应用程序？',
            confirmed: () => {
                this.thisWindow.close();
            },
        });
    }

    getVersion() {
        return this.app.contextData.appVersion;
    }

    createWinFooterApp() {

        this.about = {
            
            userName: this.userInfo.userName,
            fullName: this.userInfo.fullName,
            serverName: this.serverInfo.name,
            tradingDay: null,
            version: this.getVersion(),
        };

        this.network = { 

            type: 'success', 
            icon: 'el-icon-star-on', 
            content: '连接状态OK',
            pingpong: null,
            pingpongTitle: null,
        };

        this.messageApp = new Vue({

            el: document.querySelector('.win-footer'),
            data: {

                about: this.about,
                network: this.network,
            },

            methods: this.helper.fakeVueInsMethod(this, [this.logout]),
        });
    }

    createWinLockComponent() {

        this.locker = new WinLocker(this);
        this.locker.startup();
    }

    /**
     * @param {Boolean} is_ok 
     * @param {String} content 
     */
    handleNetworkStatusChange(is_ok, content) {

        if (is_ok) {

            this.network.type = 'success';
            this.network.icon = 'el-icon-star-on';
        }
        else {

            this.network.type = 'error';
            this.network.icon = 'el-icon-error';
        }

        this.network.content = content;
    }

    /**
     * @param {String} start_ts
     * @param {String} end_ts 
     */
    handlePingPong(start_ts, end_ts) {

        var half = Math.ceil((end_ts - start_ts) / 2);
        this.network.pingpongTitle = `发送: ${ new Date(start_ts).format('hh:mm:ss-S') } ~ 接收: ${ new Date(end_ts).format('hh:mm:ss-S') }`;
        this.network.pingpong = `网络延迟 ${ half }ms`;
    }

    /**
     * @param {Menu} menu 
     */
    openTabViewByMenu(menu) {

        this.setAsLastOpened(menu);
        let location = menu.viewLocation;
        let title = menu.menuName;
        let view_options = { permits: menu.blackPermit, menuId: menu.menuId, menuName: menu.menuName };

        if (this.helper.isNotNone(menu.menuTag)) {
            view_options.tag = menu.menuTag;
        }

        this.toptab.openTab(true, location, title, view_options);
    }

    /**
     * @param {Menu} menu 
     */
    setAsLastOpened(menu) {
        localStorage.setItem('last-opened-menu', JSON.stringify({ id: menu.menuId, name: menu.menuName }));
    }

    listen2Events() {

        this.renderProcess.on(this.systemEvent.networkStatusChange, (event, { ok, content }) => {
            this.handleNetworkStatusChange(ok, content);
        });

        this.renderProcess.on(this.systemEvent.pingPong, (event, { start, end }) => {
            this.handlePingPong(start, end);
        });
    }

    keepTradingDayUpdated() {

        let thisObj = this;
        async function execRequst() {

            let resp = await repoTradingDay.getCurrentTradingDay();
            if (resp.errorCode == 0) {
                thisObj.about.tradingDay = resp.data;
            }
        }

        setInterval(execRequst, 1000 * 60 * 3);
        execRequst();
    }

    handleViewRouting() {

        if (this.menus.length == 0) {
            return;
        }

        let last = localStorage.getItem('last-opened-menu');
        if (!last) {
            this.open1stMenu();
            return;
        }

        try {

            let { id, name } = JSON.parse(last);
            let matched = this.menus.find(x => x.menuId == id || x.children.some(y => y.menuId == id));
            if (matched == undefined) {
                this.open1stMenu();
                return;
            }
            
            let is_level1 = matched.menuId == id;
            if (is_level1) {

                this.menuStates.selectedLevel1 = matched;
                this.menuStates.selectedLevel2 = null;
                this.openTabViewByMenu(matched);
            }
            else {

                matched.expanded = true;
                let child = matched.children.find(x => x.menuId == id);
                this.menuStates.selectedLevel1 = matched;
                this.menuStates.selectedLevel2 = child;
                this.openTabViewByMenu(child);
            }
        }
        catch(ex) {
            this.open1stMenu();
        }
    }

    open1stMenu() {

        /**
         * 打开第一个菜单，作为默认视图
         */

        let first = this.menus[0];
        let has_child = first.children.length > 0;

        if (has_child) {

            first.expanded = true;
            this.menuStates.selectedLevel2 = first.children[0];
        }

        let focused = has_child ? first.children[0] : first;
        this.openTabViewByMenu(focused);
    }

    /**
     * 读取并构造系统菜单数据
     * @returns {Array<Menu>}
     */
    constructMenus() {
        
        var menus = this.app.contextData[this.dataKey.userMenus];
        if (!(menus instanceof Array) || menus.length == 0) {
            return [];
        }
        
        return menus.map(item => Menu.Duplicate(item));
    }

    prepare() {

        /**
         * 菜单列表
         */

        this.menus = this.constructMenus();
        this.menuStates = {

            selectedLevel1: this.menus[0],
            selectedLevel2: this.menus[0],
            expanded: true,
        };

        this.classes = { menuOn: 'with-menu-on' };        
        this.$menuToggleCrumb = document.querySelector('.win-content > .menu-expander');
        this.$menuToggleCrumb.onclick = this.toggleShowMenu.bind(this);
        setTimeout(() => { this.toggleShowMenu(); }, 0);
    }

    compatiblizeStyle() {

        if (this.userInfo.isTradingMan) {
            return;
        }

        var $common = document.getElementById('link-common-stylesheet');
        var $common_old = document.createElement('link');
        $common_old.rel = 'stylesheet';
        $common_old.href = '../asset/css/common-old.css';
        $common.parentElement.insertBefore($common_old, $common.nextElementSibling);

        var $theme = document.getElementById('link-theme-stylesheet');
        var $theme_old = document.createElement('link');
        $theme_old.rel = 'stylesheet';
        $theme_old.href = '../asset/css/themed-dark-old.css';
        $theme.parentElement.insertBefore($theme_old, $theme.nextElementSibling);
    }

    build() {

        this.compatiblizeStyle();
        this.prepare();
        this.setupTabCtr();
        this.brocastReady();
        this.createWinButtonsApp();
        this.createMenuApp();
        this.createWinFooterApp();
        this.createWinLockComponent();
        this.listen2Events();
        this.keepTradingDayUpdated();

        // 通知主进程，主窗口已准备就绪
        this.renderProcess.send(this.systemEvent.mainWindowReady);
        window.dashboardWindow = this;
    }
}

class WinLocker {

    get helper() {
        return this.dashboard.helper;
    }

    get systemEvent() {
        return this.dashboard.systemEvent;
    }

    get renderProcess() {
        return this.dashboard.renderProcess;
    }

    /**
     * @param {WinDashboard} dashboard 
     */
    constructor(dashboard) {
        this.dashboard = dashboard;
    }

    createApp() {

        this.data = { 
            
            passcode: null,
            displaying: false,
        };

        this.vueApp = new Vue({

            el: document.body.querySelector('.window-locker'),
            data: this.data,
            methods: this.helper.fakeVueInsMethod(this, [this.unlockScreen, this.exit]),
        });
    }

    unlockScreen() {

        let passcode = (this.data.passcode || '').trim();
        if (passcode.length == 0) {
            return;
        }

        this.renderProcess.send(this.systemEvent.unlockScreen, passcode);
        this.data.passcode = null;
    }

    exit() {

        this.dashboard.interaction.showConfirm({

			title: '操作确认',
			message: '是否确认退出软件？',
			confirmed: () => {
                this.renderProcess.send(this.systemEvent.exitApp);	
			},
		});
    }

    lock() {
        this.renderProcess.send(this.systemEvent.lockScreen);
    }

    putup() {
        this.data.displaying = true;
    }

    putdown() {
        this.data.displaying = false;
    }

    startup() {

        this.createApp();
        this.renderProcess.on(this.systemEvent.lockScreen, () => { this.putup(); });
        this.renderProcess.on(this.systemEvent.unlockScreen, () => { this.putdown(); });
    }
}

module.exports = WinDashboard;
